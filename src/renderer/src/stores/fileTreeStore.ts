import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useNodeStore } from './nodeStore'
import { BaseNode } from '@renderer/componentSystem/common/BaseNode'
import { mitter } from '@renderer/plugins/mitter'
import { useDockviewStore } from './dockviewStore'
import { v4 } from 'uuid'

export const useFileTreeStore = defineStore('fileTree', () => {
  // 选择状态管理
  const selectedNodeIds = ref<string[]>([])
  const lastSelectedNodeId = ref<string | null>(null)

  // 展开状态管理
  const expandedNodeIds = ref<Set<string>>(new Set())

  // 拖拽状态管理
  const isDraggingOverItem = ref(false)
  const isTreeAreaDragOver = ref(false)
  const isOverItemList = ref(false)

  // 计算属性：获取打开的节点ID列表
  const openedNodeIds = computed(() => {
    const dockviewStore = useDockviewStore()
    const activePanel = dockviewStore.getActivePanel()
    
    if (!activePanel || !activePanel.params) return []
    
    const params = activePanel.params as any
    // 支持所有节点类型：note, pdf, xingliu, chronengine, todo
    if (params.node && ['note', 'pdf', 'xingliu', 'chronengine', 'todo'].includes(params.type)) {
      return [params.node.uuid]
    }
    
    return []
  })

  // 仅选择节点，不触发打开操作（用于面板激活时的同步）
  const selectNodeOnly = (nodeId: string) => {
    selectedNodeIds.value = [nodeId]
    lastSelectedNodeId.value = nodeId
  }

  // 选择操作
  const selectNode = (nodeId: string, event: MouseEvent) => {
    const nodeStore = useNodeStore()

    // 如果按住Ctrl键（Mac上是Command键）
    if (event.ctrlKey || event.metaKey) {
      // 如果节点已选中，则取消选中
      if (selectedNodeIds.value.includes(nodeId)) {
        selectedNodeIds.value = selectedNodeIds.value.filter((id) => id !== nodeId)
      } else {
        // 否则添加到选中列表
        selectedNodeIds.value.push(nodeId)
      }
    }
    // 如果按住Shift键，选择范围
    else if (event.shiftKey && lastSelectedNodeId.value) {
      // 获取节点列表（扁平化的所有节点）
      const allNodes = flattenNodes(nodeStore.memoryNodes)
      // 找到上一次选中节点和当前节点的索引
      const lastIndex = allNodes.findIndex((node) => node.uuid === lastSelectedNodeId.value)
      const currentIndex = allNodes.findIndex((node) => node.uuid === nodeId)

      if (lastIndex !== -1 && currentIndex !== -1) {
        // 确定范围的开始和结束
        const start = Math.min(lastIndex, currentIndex)
        const end = Math.max(lastIndex, currentIndex)

        // 选择范围内的所有节点
        selectedNodeIds.value = allNodes.slice(start, end + 1).map((node) => node.uuid)
      }
    }
    // 普通点击，仅选择当前节点
    else {
      selectedNodeIds.value = [nodeId]
    }

    // 更新最后选中的节点
    lastSelectedNodeId.value = nodeId

    // 如果不是选择事件(没有按住Ctrl或Shift)，则打开笔记
    if (!event.ctrlKey && !event.metaKey && !event.shiftKey) {
      openNode(nodeId)
    }
  }

  // 打开节点
  const openNode = (nodeId: string) => {
    const nodeStore = useNodeStore()
    const node = nodeStore.getNodeById(nodeId) as BaseNode
    const dockviewStore = useDockviewStore()

    if (!node) return

    const activePanelId = dockviewStore.getActivePanelId()
    
    // 根据节点类型确定面板类型
    const panelType = node.type || 'note'

    if (activePanelId) {
      // 如果已存在，更新参数并激活
      dockviewStore.replaceNode(activePanelId, {
        type: panelType,
        node
      })
      // 更新面板标题
      const panel = dockviewStore.getApi()?.getPanel(activePanelId)
      if (panel) {
        panel.api.setTitle(node.title)
      }
    } else {
      // 如果不存在，创建新面板
      const panel = dockviewStore.addPanel({
        id: v4(),
        title: node.title,
        component: 'panel',
        params: {
          type: panelType,
          node
        }
      })
      if (panel) {
        dockviewStore.setActivePanelId(panel.id)
      }
    }
  }

  // 切换节点展开状态
  const toggleNodeExpanded = (nodeId: string) => {
    if (expandedNodeIds.value.has(nodeId)) {
      expandedNodeIds.value.delete(nodeId)
    } else {
      expandedNodeIds.value.add(nodeId)
    }
  }

  // 展开节点
  const expandNode = (nodeId: string) => {
    expandedNodeIds.value.add(nodeId)
  }

  // 折叠节点
  const collapseNode = (nodeId: string) => {
    expandedNodeIds.value.delete(nodeId)
  }

  // 清除所有选择
  const clearSelection = () => {
    selectedNodeIds.value = []
    lastSelectedNodeId.value = null
  }

  // 扁平化节点树函数
  const flattenNodes = (nodes: any[]): any[] => {
    let result: any[] = []

    for (const node of nodes) {
      result.push(node)
      if (node.children && node.children.length > 0) {
        result = result.concat(flattenNodes(node.children))
      }
    }

    return result
  }

  // 拖拽相关操作
  const setDraggingOverItem = (value: boolean) => {
    isDraggingOverItem.value = value
    if (value) {
      isTreeAreaDragOver.value = false
    }
  }

  const setTreeAreaDragOver = (value: boolean) => {
    if (!isDraggingOverItem.value) {
      isTreeAreaDragOver.value = value
    }
  }

  const setOverItemList = (value: boolean) => {
    isOverItemList.value = value
    if (value) {
      isTreeAreaDragOver.value = false
    }
  }

  // 处理多选拖拽移动
  const handleMultiSelectMove = async (
    sourceId: string,
    targetId: string | null,
    position: 'before' | 'after' | 'inside' | 'root'
  ) => {
    console.log('[DEBUG] handleMultiSelectMove called:', { sourceId, targetId, position })
    console.log('[DEBUG] Selected nodes:', selectedNodeIds.value)
    console.log('[DEBUG] Is source in selection:', selectedNodeIds.value.includes(sourceId))

    const nodeStore = useNodeStore()

    if (selectedNodeIds.value.includes(sourceId)) {
      console.log('[DEBUG] Processing multi-select move')
      // 对所有选中的节点执行移动操作
      for (const nodeId of selectedNodeIds.value) {
        if (position === 'root') {
          await nodeStore.moveNodeToRoot(nodeId)
        } else if (position === 'inside' && targetId) {
          await nodeStore.moveNodeToParent(nodeId, targetId)
        } else if (targetId && (position === 'before' || position === 'after')) {
          const targetNode = nodeStore.getNodeById(targetId)
          const parentId = targetNode?.parent?.uuid || null
          await nodeStore.moveNodeToPosition(nodeId, targetId, position, parentId)
        }
      }

      // 发送事件通知移动完成
      mitter.emit('node-moved', {
        sourceIds: selectedNodeIds.value,
        targetId,
        position
      })
    } else {
      console.log('[DEBUG] Processing single node move')
      // 处理单个节点移动（不在选中列表中的节点）
      if (position === 'root') {
        console.log('[DEBUG] Moving single node to root:', sourceId)
        const success = await nodeStore.moveNodeToRoot(sourceId)
        console.log('[DEBUG] Move to root result:', success)
        if (success) {
          // 发送事件通知移动完成
          mitter.emit('node-moved', {
            sourceId,
            targetId: null,
            position: 'root'
          })
        }
      } else if (position === 'inside' && targetId) {
        const success = await nodeStore.moveNodeToParent(sourceId, targetId)
        if (success) {
          mitter.emit('node-moved', {
            sourceId,
            targetId,
            position: 'inside'
          })
        }
      } else if (targetId && (position === 'before' || position === 'after')) {
        const targetNode = nodeStore.getNodeById(targetId)
        const parentId = targetNode?.parent?.uuid || null
        const success = await nodeStore.moveNodeToPosition(sourceId, targetId, position, parentId)
        if (success) {
          mitter.emit('node-moved', {
            sourceId,
            targetId,
            position
          })
        }
      }
    }
  }

  // 监听笔记创建事件，自动展开父节点
  const handleNoteCreated = (event: any) => {
    if (event.parentId) {
      expandNode(event.parentId)
    }
  }

  // 初始化事件监听
  const initializeEventListeners = () => {
    mitter.on('note-created', handleNoteCreated)
  }

  // 清理事件监听
  const cleanupEventListeners = () => {
    mitter.off('note-created', handleNoteCreated)
  }

  return {
    // 状态
    selectedNodeIds,
    lastSelectedNodeId,
    expandedNodeIds,
    isDraggingOverItem,
    isTreeAreaDragOver,
    isOverItemList,

    // 计算属性
    openedNodeIds,

    // 操作方法
    selectNode,
    selectNodeOnly,
    openNode,
    toggleNodeExpanded,
    expandNode,
    collapseNode,
    clearSelection,
    flattenNodes,
    setDraggingOverItem,
    setTreeAreaDragOver,
    setOverItemList,
    handleMultiSelectMove,
    initializeEventListeners,
    cleanupEventListeners
  }
})
