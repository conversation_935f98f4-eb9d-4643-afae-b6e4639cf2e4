import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useConfig } from '@renderer/utils/configHelper'

export interface UserInfo {
  uuid: string
  email: string
}

export type UserState = {
  token: string
  userInfo: UserInfo | null
}

export const useUserStore = defineStore(
  'user',
  () => {
    const token = ref('')
    const userInfo = ref<UserInfo | null>(null)

    function setToken(newToken: string) {
      token.value = newToken
    }

    async function setUserInfo(info: UserInfo) {
      userInfo.value = info
      
      // 确保用户目录结构存在
      try {
        const config = useConfig()
        await config.createUserDirectories(info.uuid)
      } catch (error) {
        console.error('Failed to create user directories:', error)
      }
    }

    function clearUserInfo() {
      token.value = ''
      userInfo.value = null
    }

    return {
      token,
      userInfo,
      setToken,
      setUserInfo,
      clearUserInfo
    }
  },
  {
    persist: {
      key: 'user',
      storage: localStorage
    }
  }
)
