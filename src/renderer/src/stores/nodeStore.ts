import { FileSystemManager } from '@renderer/api/uniform/fs'
import { BaseNode } from '@renderer/componentSystem/common/BaseNode'
import { useUserStore } from '@renderer/stores/user'
import { useConfig } from '@renderer/utils/configHelper'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

import { BlockJsonNode } from '@renderer/componentSystem/BlockJsonNode'
import path from 'path-browserify-esm'
import { UserBasedLocalStorage } from '@renderer/utils/UserBasedLocalStorage'

type NodeType = BaseNode

export const useNodeStore = defineStore('node', () => {
  const isRegistered = ref(false)
  const memoryNodes = ref<NodeType[]>([])

  // 添加一个响应式的触发器来强制重新计算 visibleNodes
  const trashUpdateTrigger = ref(0)

  // 过滤掉回收站中的节点的计算属性
  // 响应式地监听 trashStore 状态变化
  const visibleNodes = computed(() => {
    // 通过访问 trashUpdateTrigger 来确保响应性
    trashUpdateTrigger.value

    try {
      // 获取回收站中的 UUID 集合
      let trashUuids: Set<string>

      try {
        // 使用用户维度的 localStorage 来获取回收站数据
        const userStorage = UserBasedLocalStorage.getInstance()
        const trashItemsStr = userStorage.getItem('chronnote_trash_items')
        const trashItems = trashItemsStr ? JSON.parse(trashItemsStr) : []
        trashUuids = new Set(trashItems.map((item: any) => item.uuid))
      } catch (error) {
        console.warn('Failed to read trash items from user storage:', error)
        trashUuids = new Set()
      }

      // 如果回收站为空，直接返回原始节点
      if (trashUuids.size === 0) {
        return memoryNodes.value
      }

      // 递归过滤回收站节点，同时保持 BlockJsonNode 等类实例的原型，
      // 以确保方法（如 addNode 等）不会丢失。
      const cloneWithProto = (original: NodeType): NodeType => {
        // 使用原型创建新对象，复制可枚举属性
        const cloned = Object.create(Object.getPrototypeOf(original))
        Object.assign(cloned, original)
        return cloned
      }

      const filterTrashNodes = (nodes: NodeType[]): NodeType[] => {
        return nodes
          .filter(node => !trashUuids.has(node.uuid))
          .map(node => {
            // 如果节点有子节点，递归过滤子节点
            if (node.children && node.children.length > 0) {
              const filteredChildren = filterTrashNodes(node.children as NodeType[])

              // 如果过滤后的子节点数量与原始相同，直接返回原始节点，避免不必要的克隆
              if (filteredChildren.length === (node.children as NodeType[]).length) {
                return node
              }

              // 克隆节点以保留原型，然后替换 children
              const clonedNode = cloneWithProto(node)
              clonedNode.children = filteredChildren

              return clonedNode as NodeType
            }

            // 没有子节点，直接返回原始节点
            return node
          })
      }

      return filterTrashNodes(memoryNodes.value)
    } catch (error) {
      console.warn('Failed to filter trash nodes:', error)
      return memoryNodes.value
    }
  })

  // 提供一个方法来触发文件树更新
  const triggerTreeUpdate = () => {
    trashUpdateTrigger.value++
  }

  const initNotes = async () => {
    const userStore = useUserStore()
    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in to initialize nodes')
    }
    const fsManager = new FileSystemManager()
    const config = useConfig()
    const userWorkspace = await config.getUserWorkspace(userStore.userInfo.uuid)
    const chroniclePath = path.resolve(userWorkspace, 'blocks.json')

    try {
      // 检查文件是否存在
      await fsManager.stat(chroniclePath)
    } catch (error) {
      // 如果文件不存在，创建一个空的笔记文件
      memoryNodes.value = []
      await fsManager.writeJson(chroniclePath, JSON.stringify([]))
      return
    }

    try {
      const serializedResourceNodes = await fsManager.readJson(chroniclePath)
      //根据type类型创建不同的node
      //现在不需要根据type不同来创建了，因为现在只有一种node
      memoryNodes.value = serializedResourceNodes.map((item) => {
        return new BlockJsonNode(item)
      })
    } catch (error) {
      console.error('Error reading chronicle file:', error)
      throw new Error('Failed to load chronicle')
    }
  }
  //获取对应uuid的note
  const getNoteByUUID = (uuid: string) => {
    return memoryNodes.value.find((note) => note.uuid === uuid)
  }
  const searchNodeInTree = (id: string, nodes: NodeType[]): NodeType | null => {
    for (const node of nodes) {
      if (node.uuid === id) {
        return node
      } else if (node.children) {
        const foundNode = searchNodeInTree(id, node.children as NodeType[])
        if (foundNode !== null) {
          return foundNode
        }
      }
    }
    return null
  }

  // 根据ID获取节点
  const getNodeById = (id: string): NodeType | null => {
    return searchNodeInTree(id, memoryNodes.value)
  }

  // 将节点移动到新的父节点下
  const moveNodeToParent = async (sourceNodeId: string, targetNodeId: string): Promise<boolean> => {
    // 获取源节点和目标节点
    const sourceNode = getNodeById(sourceNodeId)
    const targetNode = getNodeById(targetNodeId)

    // 如果源节点或目标节点不存在，返回失败
    if (!sourceNode || !targetNode) return false

    // 如果源节点和目标节点相同，不执行操作
    if (sourceNodeId === targetNodeId) return false

    // 如果目标节点已经是源节点的父节点，不执行操作
    if (sourceNode.parent?.uuid === targetNodeId) return false

    // 检查目标节点是否为源节点的子节点，避免循环引用
    let parent = targetNode.parent
    while (parent) {
      if (parent.uuid === sourceNodeId) return false
      parent = parent.parent
    }

    // 从源节点的父节点中移除
    if (sourceNode.parent) {
      sourceNode.parent.children = sourceNode.parent.children.filter(
        (child) => child.uuid !== sourceNodeId
      )
    } else {
      // 如果是顶级节点，从根节点列表中移除
      memoryNodes.value = memoryNodes.value.filter((node) => node.uuid !== sourceNodeId)
    }

    // 添加到目标节点的子节点列表中
    targetNode.children.push(sourceNode)

    // 更新源节点的父节点引用
    sourceNode.parent = targetNode

    // 保存节点的变更
    if (sourceNode.saveChanges) await sourceNode.saveChanges()
    if (targetNode.saveChanges) await targetNode.saveChanges()

    return true
  }

  // 将节点移动到顶层
  const moveNodeToRoot = async (nodeId: string): Promise<boolean> => {
    // 获取源节点
    const sourceNode = getNodeById(nodeId)

    // 如果节点不存在，返回失败
    if (!sourceNode) return false

    // 如果节点已经是顶层节点，不执行操作
    if (!sourceNode.parent) return false

    // 从父节点的子节点列表中移除
    sourceNode.parent.children = sourceNode.parent.children.filter((child) => child.uuid !== nodeId)

    // 清除源节点的父节点引用
    const oldParent = sourceNode.parent
    sourceNode.parent = null

    // 添加到顶层节点列表
    memoryNodes.value.push(sourceNode)

    // 保存节点的变更
    if (sourceNode.saveChanges) await sourceNode.saveChanges()
    if (oldParent?.saveChanges) await oldParent.saveChanges()

    return true
  }

  // 将节点移动到指定位置
  const moveNodeToPosition = async (
    sourceNodeId: string,
    targetNodeId: string | null,
    position: 'before' | 'after',
    parentId: string | null = null
  ): Promise<boolean> => {
    // 获取源节点
    const sourceNode = getNodeById(sourceNodeId)
    if (!sourceNode) return false

    // 如果源节点和目标节点相同，不执行操作
    if (sourceNodeId === targetNodeId) return false

    // 保存旧的父节点
    const oldParent = sourceNode.parent

    // 从原位置移除源节点
    if (sourceNode.parent) {
      sourceNode.parent.children = sourceNode.parent.children.filter(
        (child) => child.uuid !== sourceNodeId
      )
    } else {
      memoryNodes.value = memoryNodes.value.filter((node) => node.uuid !== sourceNodeId)
    }

    // 如果有父节点ID，说明是插入到某个父节点的子节点列表中
    if (parentId) {
      const parentNode = getNodeById(parentId)
      if (!parentNode) {
        // 如果找不到父节点，恢复原状态
        if (oldParent) {
          oldParent.children.push(sourceNode)
        } else {
          memoryNodes.value.push(sourceNode)
        }
        return false
      }

      // 设置新的父节点
      sourceNode.parent = parentNode

      // 如果没有目标节点，直接添加到末尾
      if (!targetNodeId) {
        parentNode.children.push(sourceNode)
      } else {
        // 找到目标节点在父节点子列表中的位置
        const targetIndex = parentNode.children.findIndex((child) => child.uuid === targetNodeId)
        if (targetIndex === -1) {
          // 如果找不到目标节点，添加到末尾
          parentNode.children.push(sourceNode)
        } else {
          // 根据位置插入
          const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
          parentNode.children.splice(insertIndex, 0, sourceNode)
        }
      }

      // 保存父节点的变更
      if (parentNode.saveChanges) await parentNode.saveChanges()
    } else {
      // 插入到顶层
      sourceNode.parent = null

      // 如果没有目标节点，直接添加到末尾
      if (!targetNodeId) {
        memoryNodes.value.push(sourceNode)
      } else {
        // 找到目标节点在顶层列表中的位置
        const targetIndex = memoryNodes.value.findIndex((node) => node.uuid === targetNodeId)
        if (targetIndex === -1) {
          // 如果找不到目标节点，添加到末尾
          memoryNodes.value.push(sourceNode)
        } else {
          // 根据位置插入
          const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
          memoryNodes.value.splice(insertIndex, 0, sourceNode)
        }
      }
    }

    // 保存源节点的变更
    if (sourceNode.saveChanges) await sourceNode.saveChanges()

    // 如果旧父节点存在且不同于新父节点，保存其变更
    if (oldParent && oldParent.uuid !== parentId && oldParent.saveChanges) {
      await oldParent.saveChanges()
    }

    return true
  }

  // // 创建子笔记
  // const createChildNote = (parentId: string, options?: { shouldScroll?: boolean }) => {
  //   const parentNode = getNodeById(parentId)
  //   if (!parentNode) return null

  //   const newNote = parentNode.addNode('新笔记')

  //   mitter.emit('note-created', {
  //     parentId: parentId,
  //     noteId: newNote.uuid,
  //     shouldScroll: options?.shouldScroll ?? false
  //   })

  //   return newNote
  // }

  // 清理数据方法
  const clearData = () => {
    console.log('清理 nodeStore 数据...')
    memoryNodes.value = []
    isRegistered.value = false
    // 移除 triggerTreeUpdate() - memoryNodes 的响应式变化已足够触发 UI 更新
  }

  return {
    initNotes,
    getNoteByUUID,
    isRegistered,
    memoryNodes,
    visibleNodes,
    searchNodeInTree,
    getNodeById,
    moveNodeToParent,
    moveNodeToRoot,
    moveNodeToPosition,
    triggerTreeUpdate,
    clearData
  }
})
