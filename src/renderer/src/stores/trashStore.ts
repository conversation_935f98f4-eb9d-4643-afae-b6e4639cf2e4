import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'
import { useGlobalStore } from './globalStore'
import { useNodeStore } from './nodeStore'
import type { BlockJsonNode } from '../componentSystem/BlockJsonNode'
import { useLocalJson } from '../composables/useLocalJson'
import { UserBasedLocalStorage } from '../utils/UserBasedLocalStorage'

// 回收站项目接口
export interface TrashItem {
  uuid: string
  originalNode: {
    uuid: string
    title: string
    type: string
    children: string[] // 只保存子节点的UUID
    parent: string | null // 只保存父节点的UUID
    meta: any[]
    createdAt: number
    updatedAt: number
  }
  deletedAt: number
  deletedBy?: string
}

// 旧的回收站存储键（用于数据迁移）
const LEGACY_TRASH_STORAGE_KEY = 'chronnote_trash_items'

export const useTrashStore = defineStore('trash', () => {
  const globalStore = useGlobalStore()
  const nodeStore = useNodeStore()

  // 使用新的 useLocalJson 存储系统
  const { get, set, data: jsonData, load: loadJsonData } = useLocalJson('trash')

  // 响应式的垃圾回收站数据
  const trashItems = ref<TrashItem[]>([])

  // 初始化状态
  const isInitialized = ref(false)
  const initializationPromise = ref<Promise<void> | null>(null)

  /**
   * 数据迁移：从旧的 UserBasedLocalStorage 迁移到新的 useLocalJson
   */
  const migrateFromLegacyStorage = async (): Promise<void> => {
    try {
      // 确保 JSON 数据已加载
      await loadJsonData()

      // 检查新存储中是否已有数据
      const existingItems = get<TrashItem[]>('items', [])
      if (existingItems.length > 0) {
        console.log('New storage already has data, skipping migration')
        trashItems.value = existingItems
        return
      }

      // 尝试从旧存储中读取数据
      const userStorage = UserBasedLocalStorage.getInstance()
      const legacyDataStr = userStorage.getItem(LEGACY_TRASH_STORAGE_KEY)

      if (legacyDataStr) {
        try {
          const legacyData: TrashItem[] = JSON.parse(legacyDataStr)
          if (Array.isArray(legacyData) && legacyData.length > 0) {
            console.log(`Migrating ${legacyData.length} trash items from legacy storage`)

            // 迁移数据到新存储
            set('items', legacyData)
            trashItems.value = legacyData

            // 清除旧存储中的数据（可选，为了安全可以保留一段时间）
            // userStorage.removeItem(LEGACY_TRASH_STORAGE_KEY)

            console.log('Migration completed successfully')
          } else {
            // 初始化为空数组
            trashItems.value = []
            set('items', [])
          }
        } catch (parseError) {
          console.warn('Failed to parse legacy trash data:', parseError)
          trashItems.value = []
          set('items', [])
        }
      } else {
        // 没有旧数据，初始化为空数组
        trashItems.value = []
        set('items', [])
      }
    } catch (error) {
      console.error('Migration from legacy storage failed:', error)
      // 发生错误时，初始化为空数组
      trashItems.value = []
      set('items', [])
    }
  }

  /**
   * 同步数据到存储
   */
  const syncToStorage = (): void => {
    set('items', trashItems.value)
  }

  /**
   * 从存储加载数据
   */
  const loadFromStorage = async (): Promise<void> => {
    try {
      // 确保 JSON 数据已加载
      await loadJsonData()

      const items = get<TrashItem[]>('items', [])
      trashItems.value = Array.isArray(items) ? items : []

      console.log(`Loaded ${trashItems.value.length} items from trash storage`)
    } catch (error) {
      console.error('Failed to load from storage:', error)
      trashItems.value = []
    }
  }

  /**
   * 初始化垃圾回收站
   */
  const initialize = async (): Promise<void> => {
    if (isInitialized.value || initializationPromise.value) {
      return initializationPromise.value || Promise.resolve()
    }

    initializationPromise.value = (async () => {
      try {
        console.log('Initializing trash store...')

        // 先尝试迁移，然后加载数据
        await migrateFromLegacyStorage()
        await loadFromStorage()

        isInitialized.value = true
        console.log('Trash store initialized successfully')
      } catch (error) {
        console.error('Failed to initialize trash store:', error)
        // 即使初始化失败，也要设置为已初始化，避免重复尝试
        isInitialized.value = true
        trashItems.value = []
      }
    })()

    return initializationPromise.value
  }

  // 监听 JSON 数据变化，同步到本地状态
  watch(
    () => jsonData.value,
    (newData) => {
      if (newData && typeof newData === 'object') {
        const items = newData.items
        if (Array.isArray(items)) {
          trashItems.value = items
          // 触发节点树更新
          nodeStore.triggerTreeUpdate()
        }
      }
    },
    { deep: true, immediate: true }
  )

  // 立即开始初始化
  initialize()



  // 计算属性
  const trashCount = computed(() => trashItems.value.length)
  const isEmpty = computed(() => trashItems.value.length === 0)
  const isTrashOpen = computed(() => globalStore.isTrashOpen)

  // 按删除时间排序的回收站项目（最新的在前）
  const sortedTrashItems = computed(() => {
    return [...trashItems.value].sort((a, b) => b.deletedAt - a.deletedAt)
  })



  /**
   * 递归获取节点的所有子节点
   * @param node 父节点
   * @returns 所有子节点的数组（包括子节点的子节点）
   */
  const getAllChildrenRecursively = (node: BlockJsonNode): BlockJsonNode[] => {
    const children: BlockJsonNode[] = []

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        const childNode = child as BlockJsonNode
        children.push(childNode)
        // 递归获取子节点的子节点
        children.push(...getAllChildrenRecursively(childNode))
      }
    }

    return children
  }

  /**
   * 添加项目到回收站（支持级联删除）
   * @param node 要添加到回收站的节点
   */
  const addToTrash = async (node: BlockJsonNode): Promise<void> => {
    try {
      // 确保垃圾回收站已初始化
      await initialize()

      // 获取所有需要删除的节点（包括子节点）
      const nodesToDelete = [node, ...getAllChildrenRecursively(node)]

      // 为每个节点创建回收站项目
      const newTrashItems: TrashItem[] = []
      const deletedAt = Date.now()

      for (const nodeToDelete of nodesToDelete) {
        // 检查节点是否已经在回收站中，避免重复添加
        if (!isInTrash(nodeToDelete.uuid)) {
          const trashItem: TrashItem = {
            uuid: nodeToDelete.uuid,
            originalNode: {
              uuid: nodeToDelete.uuid,
              title: nodeToDelete.title,
              type: nodeToDelete.type,
              children: nodeToDelete.children ? nodeToDelete.children.map(child => child.uuid) : [],
              parent: nodeToDelete.parent ? nodeToDelete.parent.uuid : null,
              meta: nodeToDelete.meta || [],
              createdAt: nodeToDelete.createdAt || Date.now(),
              updatedAt: nodeToDelete.updatedAt || Date.now()
            },
            deletedAt: deletedAt
          }
          newTrashItems.push(trashItem)
        }
      }

      // 批量添加到回收站
      trashItems.value.push(...newTrashItems)

      // 同步到存储（使用新的 useLocalJson 系统）
      syncToStorage()

      // 触发文件树更新
      try {
        const { useNodeStore } = await import('./nodeStore')
        const nodeStore = useNodeStore()
        nodeStore.triggerTreeUpdate()
      } catch (error) {
        console.warn('Failed to trigger tree update:', error)
      }

      console.log(`已将 ${newTrashItems.length} 个项目添加到回收站（包括子节点）:`, newTrashItems.map(item => item.originalNode.title))
    } catch (error) {
      console.error('添加到回收站失败:', error)
      throw error
    }
  }

  /**
   * 获取节点的所有子节点UUID（在回收站中）
   * @param parentUuid 父节点UUID
   * @returns 子节点UUID数组
   */
  const getChildrenInTrash = (parentUuid: string): string[] => {
    return trashItems.value
      .filter(item => item.originalNode.parent === parentUuid)
      .map(item => item.uuid)
  }

  /**
   * 检查父节点是否存在且未在回收站中
   * @param parentUuid 父节点UUID
   * @returns 父节点是否有效
   */
  const isParentValid = (parentUuid: string | null): boolean => {
    if (!parentUuid) return true // null parent 是有效的（顶级节点）

    const node = nodeStore.getNodeById(parentUuid)
    return node !== null && !isInTrash(parentUuid)
  }

  /**
   * 从回收站恢复项目（智能恢复逻辑）
   * @param uuid 要恢复的项目UUID
   * @returns 恢复的项目，如果失败则返回null
   */
  const restoreItem = async (uuid: string): Promise<TrashItem | null> => {
    try {
      // 确保垃圾回收站已初始化
      await initialize()

      const itemIndex = trashItems.value.findIndex(item => item.uuid === uuid)
      if (itemIndex === -1) {
        console.error('在回收站中找不到项目:', uuid)
        return null
      }

      const item = trashItems.value[itemIndex]
      const parentUuid = item.originalNode.parent

      // 检查父节点是否在回收站中
      const isParentInTrash = parentUuid ? isInTrash(parentUuid) : false

      if (isParentInTrash) {
        // 情况1: 父节点也在回收站中，只恢复当前节点并设为顶级节点
        console.log(`父节点 ${parentUuid} 仍在回收站中，将节点 ${uuid} 设为顶级节点`)

        // 使用nodeStore的物理层面方法将节点移动到顶级
        const success = await nodeStore.moveNodeToRoot(uuid)
        if (success) {
          console.log(`已通过nodeStore将节点 ${uuid} 移动到顶级`)
        } else {
          console.warn(`通过nodeStore移动节点 ${uuid} 到顶级失败，尝试手动处理`)
          // 如果nodeStore方法失败，手动处理
          const node = nodeStore.getNodeById(uuid)
          if (node) {
            // 从原父节点的children中移除
            if (node.parent) {
              node.parent.children = node.parent.children.filter(child => child.uuid !== uuid)
              if (node.parent.saveChanges) {
                await node.parent.saveChanges()
              }
            }
            // 设为顶级节点
            node.parent = null
            // 添加到根节点列表
            if (!nodeStore.memoryNodes.includes(node)) {
              nodeStore.memoryNodes.push(node)
            }
            // 保存节点变更
            if (node.saveChanges) {
              await node.saveChanges()
            }
            // 保存到磁盘
            const { BlockJsonNode } = await import('../componentSystem/BlockJsonNode')
            await BlockJsonNode.saveToDisk()
          }
        }

        // 从回收站中移除当前项目
        trashItems.value.splice(itemIndex, 1)

        // 同步到存储
        syncToStorage()

        console.log(`已将节点 ${item.originalNode.title} 恢复为顶级节点`)
      } else {
        // 情况2: 父节点不在回收站中（或为null），级联恢复所有子节点
        // 使用广度优先遍历，确保恢复所有后代
        const queue: string[] = [uuid]
        const itemsToRestoreSet: Set<string> = new Set()

        while (queue.length > 0) {
          const currentUuid = queue.shift()!
          if (itemsToRestoreSet.has(currentUuid)) continue

          itemsToRestoreSet.add(currentUuid)

          // 把当前节点的直接子节点加入队列
          const children = getChildrenInTrash(currentUuid)
          queue.push(...children)
        }

        const itemsToRestore = Array.from(itemsToRestoreSet)

        // 批量从回收站中移除所有相关项目
        const restoredItems: TrashItem[] = []
        for (const itemUuid of itemsToRestore) {
          const index = trashItems.value.findIndex(item => item.uuid === itemUuid)
          if (index !== -1) {
            const restoredItem = trashItems.value.splice(index, 1)[0]
            restoredItems.push(restoredItem)
          }
        }

        // 同步到存储
        syncToStorage()

        console.log(`已从回收站恢复 ${restoredItems.length} 个项目（包括子节点）:`, restoredItems.map(item => item.originalNode.title))
      }

      // 触发文件树更新
      try {
        const { useNodeStore } = await import('./nodeStore')
        const nodeStore = useNodeStore()
        nodeStore.triggerTreeUpdate()
      } catch (error) {
        console.warn('Failed to trigger tree update:', error)
      }

      return item
    } catch (error) {
      console.error('从回收站恢复项目失败:', error)
      return null
    }
  }

  /**
   * 处理孤儿节点（当父节点被永久删除时）
   * @param deletedParentUuid 被删除的父节点UUID
   */
  const handleOrphanNodes = async (deletedParentUuid: string): Promise<void> => {
    // 查找所有以被删除节点为父节点的回收站项目
    const orphanItems = trashItems.value.filter(item =>
      item.originalNode.parent === deletedParentUuid
    )

    for (const orphanItem of orphanItems) {
      console.log(`处理孤儿节点: ${orphanItem.originalNode.title} (${orphanItem.uuid})`)

      // 将回收站项目的父节点设置为null
      orphanItem.originalNode.parent = null

      // 如果节点在内存中存在，使用nodeStore的方法将其移动到顶级
      const memoryNode = nodeStore.getNodeById(orphanItem.uuid)
      if (memoryNode && memoryNode.parent) {
        console.log(`将孤儿节点 ${orphanItem.originalNode.title} 移动到顶级`)
        const success = await nodeStore.moveNodeToRoot(orphanItem.uuid)
        if (!success) {
          console.warn(`移动孤儿节点 ${orphanItem.uuid} 到顶级失败，手动处理`)
          // 手动处理
          memoryNode.parent.children = memoryNode.parent.children.filter(child => child.uuid !== orphanItem.uuid)
          if (memoryNode.parent.saveChanges) {
            await memoryNode.parent.saveChanges()
          }
          memoryNode.parent = null
          if (!nodeStore.memoryNodes.includes(memoryNode)) {
            nodeStore.memoryNodes.push(memoryNode)
          }
          if (memoryNode.saveChanges) {
            await memoryNode.saveChanges()
          }
          // 保存到磁盘
          const { BlockJsonNode } = await import('../componentSystem/BlockJsonNode')
          await BlockJsonNode.saveToDisk()
        }
      }
    }
  }

  /**
   * 从父节点的children数组中移除引用（使用物理层面更新）
   * @param nodeUuid 要移除的节点UUID
   */
  const removeFromParentChildren = async (nodeUuid: string): Promise<void> => {
    const node = nodeStore.getNodeById(nodeUuid)
    if (node && node.parent) {
      const parent = node.parent
      parent.children = parent.children.filter(child => child.uuid !== nodeUuid)

      // 保存父节点的变更到数据库和磁盘
      if (parent.saveChanges) {
        await parent.saveChanges()
      }

      // 保存到磁盘
      const { BlockJsonNode } = await import('../componentSystem/BlockJsonNode')
      await BlockJsonNode.saveToDisk()

      console.log(`已从父节点 ${parent.title} 中移除子节点引用: ${nodeUuid}`)
    }
  }

  /**
   * 物理删除节点（真正从内存和磁盘中删除）
   * @param nodeUuid 要删除的节点UUID
   */
  const physicallyDeleteNode = async (nodeUuid: string): Promise<void> => {
    const node = nodeStore.getNodeById(nodeUuid)
    if (!node) {
      console.warn(`节点 ${nodeUuid} 不存在于内存中，跳过物理删除`)
      return
    }

    console.log(`开始物理删除节点: ${node.title} (${nodeUuid})`)

    // 1. 从父节点的children中移除引用并保存变更
    if (node.parent) {
      const parent = node.parent
      parent.children = parent.children.filter(child => child.uuid !== nodeUuid)

      // 保存父节点变更到数据库和磁盘
      if (parent.saveChanges) {
        await parent.saveChanges()
      }
      console.log(`已从父节点 ${parent.title} 中移除引用`)
    } else {
      // 如果是顶级节点，从根节点列表中移除
      const index = nodeStore.memoryNodes.indexOf(node)
      if (index !== -1) {
        nodeStore.memoryNodes.splice(index, 1)
        console.log(`已从根节点列表中移除节点`)
      }
    }

    // 2. 调用节点的removeNode方法进行完整的物理删除
    try {
      await (node as any).removeNode()
      console.log(`节点 ${node.title} 已完成物理删除`)
    } catch (error) {
      console.error(`物理删除节点 ${node.title} 失败:`, error)
      throw error
    }

    // 3. 确保保存到磁盘
    try {
      const { BlockJsonNode } = await import('../componentSystem/BlockJsonNode')
      await BlockJsonNode.saveToDisk()
      console.log(`节点删除后已保存到磁盘`)
    } catch (error) {
      console.error(`保存到磁盘失败:`, error)
    }
  }

  /**
   * 永久删除回收站中的项目（支持引用清理和孤儿节点处理）
   * @param uuid 要永久删除的项目UUID
   * @returns 是否删除成功
   */
  const permanentlyDelete = async (uuid: string): Promise<boolean> => {
    try {
      // 确保垃圾回收站已初始化
      await initialize()

      const itemIndex = trashItems.value.findIndex(item => item.uuid === uuid)
      if (itemIndex === -1) {
        console.error('在回收站中找不到项目:', uuid)
        return false
      }

      const item = trashItems.value[itemIndex]

      // 1. 处理孤儿节点 - 将所有以当前节点为父节点的回收站项目设为顶级节点
      await handleOrphanNodes(uuid)

      // 2. 清理面板引用
      try {
        const { mitter } = await import('../plugins/mitter')
        const { track } = await import('../utils/tracks')

        // 发送删除事件
        mitter.emit('resource-deleted', {
          nodeId: uuid,
          nodeType: item.originalNode.type
        })

        // 记录操作
        track({
          action_type: 'Trash',
          extra1: 'Permanent Delete'
        })
      } catch (error) {
        console.warn('清理面板引用失败:', error)
      }

      // 3. 执行物理删除（包括从父节点children中移除和真正的节点删除）
      await physicallyDeleteNode(uuid)

      // 4. 从回收站中移除
      trashItems.value.splice(itemIndex, 1)

      // 同步到存储
      syncToStorage()

      console.log('项目已永久删除:', item.originalNode.title)
      return true
    } catch (error) {
      console.error('永久删除项目失败:', error)
      return false
    }
  }

  /**
   * 清空回收站（优化版本，按层级顺序删除）
   */
  const clearTrash = async (): Promise<void> => {
    try {
      // 确保垃圾回收站已初始化
      await initialize()

      // 获取所有回收站项目的副本
      const itemsToDelete = [...trashItems.value]

      if (itemsToDelete.length === 0) {
        console.log('回收站已经是空的')
        return
      }

      // 按层级排序：先删除子节点，再删除父节点
      // 这样可以避免在删除过程中出现引用问题
      const sortedItems = itemsToDelete.sort((a, b) => {
        // 计算节点的层级深度
        const getDepth = (item: TrashItem): number => {
          let depth = 0
          let currentParentUuid = item.originalNode.parent
          const visited = new Set<string>() // 防止循环引用

          while (currentParentUuid && !visited.has(currentParentUuid)) {
            visited.add(currentParentUuid)
            depth++
            // 查找父节点在回收站中的信息
            const parentInTrash = itemsToDelete.find(i => i.uuid === currentParentUuid)
            currentParentUuid = parentInTrash?.originalNode.parent || null
          }

          return depth
        }

        // 深度大的先删除（子节点先删除）
        return getDepth(b) - getDepth(a)
      })

      console.log(`开始清空回收站，共 ${sortedItems.length} 个项目`)

      // 逐个永久删除所有项目
      for (const item of sortedItems) {
        await permanentlyDelete(item.uuid)
      }

      // 确保清空后同步到存储
      trashItems.value = []
      syncToStorage()

      console.log('回收站已清空')
    } catch (error) {
      console.error('清空回收站失败:', error)
      throw error
    }
  }

  /**
   * 打开回收站
   */
  const openTrash = (): void => {
    globalStore.isTrashOpen = true
    globalStore.isMessagePageOpen = false
  }

  /**
   * 关闭回收站
   */
  const closeTrash = (): void => {
    globalStore.isTrashOpen = false
  }

  /**
   * 检查项目是否在回收站中
   * @param uuid 项目UUID
   * @returns 是否在回收站中
   */
  const isInTrash = (uuid: string): boolean => {
    return trashItems.value.some(item => item.uuid === uuid)
  }

  /**
   * 获取回收站中的项目
   * @param uuid 项目UUID
   * @returns 回收站项目，如果不存在则返回null
   */
  const getTrashItem = (uuid: string): TrashItem | null => {
    return trashItems.value.find(item => item.uuid === uuid) || null
  }

  /**
   * 获取节点在回收站中的所有子节点数量
   * @param uuid 节点UUID
   * @returns 子节点数量
   */
  const getChildrenCountInTrash = (uuid: string): number => {
    const directChildren = getChildrenInTrash(uuid)
    let totalCount = directChildren.length

    // 递归计算所有层级的子节点
    for (const childUuid of directChildren) {
      totalCount += getChildrenCountInTrash(childUuid)
    }

    return totalCount
  }

  /**
   * 检查节点是否有子节点在回收站中
   * @param uuid 节点UUID
   * @returns 是否有子节点
   */
  const hasChildrenInTrash = (uuid: string): boolean => {
    return getChildrenInTrash(uuid).length > 0
  }

  /**
   * 获取节点的完整层级路径（用于调试）
   * @param uuid 节点UUID
   * @returns 层级路径字符串
   */
  const getNodePath = (uuid: string): string => {
    const item = getTrashItem(uuid)
    if (!item) return 'Unknown'

    const path: string[] = [item.originalNode.title]
    let currentParentUuid = item.originalNode.parent
    const visited = new Set<string>() // 防止循环引用

    while (currentParentUuid && !visited.has(currentParentUuid)) {
      visited.add(currentParentUuid)
      const parentItem = getTrashItem(currentParentUuid)
      if (parentItem) {
        path.unshift(parentItem.originalNode.title)
        currentParentUuid = parentItem.originalNode.parent
      } else {
        // 父节点不在回收站中，尝试从内存中获取
        const memoryNode = nodeStore.getNodeById(currentParentUuid)
        if (memoryNode) {
          path.unshift(memoryNode.title)
          currentParentUuid = memoryNode.parent ? memoryNode.parent.uuid : null
        } else {
          path.unshift('(已删除)')
          break
        }
      }
    }

    return path.join(' > ')
  }



  return {
    // 状态
    trashItems,
    trashCount,
    isEmpty,
    isTrashOpen,
    sortedTrashItems,
    isInitialized,

    // 初始化方法
    initialize,

    // 基础方法
    addToTrash,
    restoreItem,
    permanentlyDelete,
    clearTrash,
    openTrash,
    closeTrash,
    isInTrash,
    getTrashItem,

    // 嵌套处理相关方法
    getAllChildrenRecursively,
    getChildrenInTrash,
    getChildrenCountInTrash,
    hasChildrenInTrash,
    isParentValid,
    getNodePath,
    handleOrphanNodes,
    removeFromParentChildren
  }
})
