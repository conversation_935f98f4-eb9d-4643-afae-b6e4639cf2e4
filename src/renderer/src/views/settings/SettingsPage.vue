<template>
  <div class="flex flex-col h-screen select-none text-sm">
    <!-- 固定的头部区域 -->
    <div class="border-b border-base-border px-6 mt-4">
      <!-- 返回按钮和标题 -->
      <div class="flex items-center gap-4 mb-2">
        <button
          @click="globalStore.isSettingsOpen = false"
          class="p-2 rounded-lg hover:bg-base-100 text-base-content-secondary hover:text-base-content transition-colors"
        >
          <ArrowLeft class="h-4.5 w-4.5" />
        </button>
        <h1 class="font-medium text-title">设置</h1>
      </div>
    </div>

    <!-- 可滚动的设置区域 -->
    <div
      class="flex-1 overflow-y-auto scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-250"
    >
      <div class="max-w-2xl mx-auto px-6 py-8 space-y-8">
        <!-- 通用设置 -->
        <section class="rounded-lg border border-base-border overflow-hidden">
          <div class="px-6 py-4 border-b border-base-border">
            <h2 class="text-lg font-medium text-title flex items-center gap-2">
              <User class="w-5 h-5" />
              通用设置
            </h2>
          </div>

          <div class="divide-y divide-base-200">
            <!-- 用户邮箱 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Mail class="w-4 h-4" />
                  邮箱
                </div>
                <div class="text-sm text-base-content-secondary mt-1">当前登录的用户邮箱</div>
              </div>
              <div class="text-sm text-base-content">
                {{ userStore.userInfo?.email || '未设置' }}
              </div>
            </div>

            <!-- 主题设置 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Palette class="w-4 h-4" />
                  主题模式
                </div>
                <div class="text-sm text-base-content-secondary mt-1">选择应用的外观主题</div>
              </div>
              <div class="flex gap-1">
                <button
                  class="px-3 py-1.5 rounded-md text-sm transition-colors"
                  :class="
                    settingsStore.themeMode === 'light'
                      ? 'bg-info/20 text-info border border-info/30'
                      : 'text-base-content-secondary hover:bg-base-200'
                  "
                  @click="handleThemeChange('light')"
                >
                  <Sun class="w-4 h-4" />
                </button>
                <button
                  class="px-3 py-1.5 rounded-md text-sm transition-colors"
                  :class="
                    settingsStore.themeMode === 'dark'
                      ? 'bg-info/20 text-info border border-info/30'
                      : 'text-base-content-secondary hover:bg-base-200'
                  "
                  @click="handleThemeChange('dark')"
                >
                  <Moon class="w-4 h-4" />
                </button>
                <button
                  class="px-3 py-1.5 rounded-md text-sm transition-colors"
                  :class="
                    settingsStore.themeMode === 'system'
                      ? 'bg-info/20 text-info border border-info/30'
                      : 'text-base-content-secondary hover:bg-base-200'
                  "
                  @click="handleThemeChange('system')"
                >
                  <Monitor class="w-4 h-4" />
                </button>
              </div>
            </div>

            <!-- 检查更新 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <RefreshCw class="w-4 h-4" />
                  检查更新
                </div>
                <div class="text-sm text-base-content-secondary mt-1">
                  当前版本 v{{ currentVersion }}
                </div>
              </div>
              <button
                @click="checkForUpdates"
                class="px-3 py-1.5 text-sm rounded-md bg-base-100 hover:bg-base-200 text-base-content transition-colors disabled:opacity-50"
                :disabled="isCheckingUpdate"
              >
                <RefreshCw
                  class="w-4 h-4 inline mr-1"
                  :class="{ 'animate-spin': isCheckingUpdate }"
                />
                {{ isCheckingUpdate ? '检查中' : '检查更新' }}
              </button>
            </div>

            <!-- 交流反馈 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <MessageCircle class="w-4 h-4" />
                  交流反馈
                </div>
                <div class="text-sm text-base-content-secondary mt-1">加入社区或联系我们</div>
              </div>
              <div class="flex gap-2">
                <button
                  class="p-2 rounded-md hover:bg-base-200 transition-colors"
                  @click="openFeedback('qq')"
                  title="QQ群"
                >
                  <img
                    src="@renderer/assets/images/qq-icon.svg"
                    alt="QQ"
                    class="w-4 h-4"
                    :class="{ invert: settingsStore.themeMode === 'dark' }"
                  />
                </button>
                <button
                  class="p-2 rounded-md hover:bg-base-200 transition-colors"
                  @click="openFeedback('wechat')"
                  title="微信群"
                >
                  <img
                    src="@renderer/assets/images/wechat.svg"
                    alt="微信"
                    class="w-4 h-4"
                    :class="{ invert: settingsStore.themeMode === 'dark' }"
                  />
                </button>
              </div>
            </div>

            <!-- 退出登录 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <LogOut class="w-4 h-4" />
                  退出登录
                </div>
                <div class="text-sm text-base-content-secondary mt-1">登出当前账户</div>
              </div>
              <button
                @click="handleLogout"
                class="px-3 py-1.5 text-sm rounded-md bg-error/10 hover:bg-error/20 text-error transition-colors"
              >
                退出
              </button>
            </div>
          </div>
        </section>

        <!-- 快捷键设置 -->
        <section class="rounded-lg border border-base-border overflow-hidden">
          <div class="px-6 py-4 border-b border-base-border">
            <h2 class="text-lg font-medium text-title flex items-center gap-2">
              <Keyboard class="w-5 h-5" />
              快捷键设置
            </h2>
          </div>

          <div class="p-6">
            <div v-for="category in keyboardCategories" :key="category" class="mb-8 last:mb-0">
              <div class="flex items-center gap-2 mb-4">
                <h3 class="text-sm font-semibold text-base-content uppercase tracking-wide">
                  {{ category }}
                </h3>
                <span
                  class="text-xs text-base-content-secondary bg-base-150 px-2 py-1 rounded-full"
                >
                  {{ getShortcutsByCategory(category).length }} 个快捷键
                </span>
              </div>

              <div class="space-y-2">
                <div
                  v-for="shortcut in getShortcutsByCategory(category)"
                  :key="shortcut.id"
                  class="flex items-center justify-between py-3 px-4 rounded-lg border border-base-border hover:border-base-border transition-colors"
                  :class="{
                    'cursor-pointer hover:bg-base-150': !shortcut.readonly,
                    'opacity-60': shortcut.readonly
                  }"
                  @click="!shortcut.readonly && openShortcutModal(shortcut)"
                >
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <span class="text-sm font-medium text-base-content">{{ shortcut.name }}</span>
                      <Lock v-if="shortcut.readonly" class="w-3 h-3 text-base-content-tertiary" />
                    </div>
                    <div class="text-xs text-base-content-secondary mt-1">
                      {{ shortcut.description }}
                    </div>
                  </div>

                  <div class="flex items-center gap-2">
                    <div class="flex items-center gap-1">
                      <template
                        v-for="(key, index) in formatShortcutKeys(shortcut.keys)"
                        :key="index"
                      >
                        <kbd
                          class="px-2 py-1 text-xs font-mono bg-base-200 border border-base-border rounded text-base-content"
                        >
                          {{ key }}
                        </kbd>
                        <span
                          v-if="index < formatShortcutKeys(shortcut.keys).length - 1"
                          class="text-base-content-tertiary text-xs"
                          >+</span
                        >
                      </template>
                    </div>
                    <button
                      v-if="!shortcut.readonly"
                      class="p-1 rounded hover:bg-base-150 text-base-content-tertiary hover:text-base-content-secondary transition-colors"
                      @click.stop="openShortcutModal(shortcut)"
                    >
                      <Edit class="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 待办事项设置 -->
        <section class="rounded-lg border border-base-border overflow-hidden">
          <div class="px-6 py-4 border-b border-base-border">
            <h2 class="text-lg font-medium text-title flex items-center gap-2">
              <ListChecks class="w-5 h-5" />
              待办事项设置
            </h2>
          </div>

          <div class="divide-y divide-base-200">
            <!-- 启用待办事项通知 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Bell class="w-4 h-4" />
                  启用待办事项通知
                </div>
                <div class="text-sm text-base-content-secondary mt-1">当待办事项到期时发送通知</div>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  class="sr-only peer"
                  v-model="settingsStore.todoNotificationEnabled"
                />
                <div
                  class="w-11 h-6 bg-base-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-info/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-base-400 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-info"
                ></div>
              </label>
            </div>

            <!-- 音效设置 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Volume2 class="w-4 h-4" />
                  启用通知音效
                </div>
                <div class="text-sm text-base-content-secondary mt-1">通知时播放提示音</div>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  class="sr-only peer"
                  v-model="settingsStore.enableSoundEffects"
                />
                <div
                  class="w-11 h-6 bg-base-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-info/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-base-400 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-info"
                ></div>
              </label>
            </div>

            <!-- 检查间隔 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Clock class="w-4 h-4" />
                  检查间隔
                </div>
                <div class="text-sm text-base-content-secondary mt-1">检查待办事项的时间间隔</div>
              </div>
              <select
                v-model="settingsStore.todoNotificationCheckInterval"
                class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info focus:border-info"
              >
                <option :value="1">1分钟</option>
                <option :value="5">5分钟</option>
                <option :value="10">10分钟</option>
                <option :value="15">15分钟</option>
                <option :value="30">30分钟</option>
              </select>
            </div>

            <!-- 到期当天通知 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <CalendarClock class="w-4 h-4" />
                  到期当天通知
                </div>
                <div class="text-sm text-base-content-secondary mt-1">在到期当天发送提醒</div>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  class="sr-only peer"
                  v-model="settingsStore.todoNotificationOnDueDate"
                />
                <div
                  class="w-11 h-6 bg-base-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-info/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-base-400 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-info"
                ></div>
              </label>
            </div>

            <!-- 过期后通知 -->
            <div class="px-6 py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <AlertCircle class="w-4 h-4" />
                  过期后通知
                </div>
                <div class="text-sm text-base-content-secondary mt-1">待办事项过期后继续提醒</div>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  class="sr-only peer"
                  v-model="settingsStore.todoNotificationExpired"
                />
                <div
                  class="w-11 h-6 bg-base-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-info/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-base-400 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-info"
                ></div>
              </label>
            </div>

            <!-- 过期通知窗口 -->
            <div
              v-if="settingsStore.todoNotificationExpired"
              class="px-6 py-4 flex items-center justify-between"
            >
              <div>
                <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                  <Timer class="w-4 h-4" />
                  过期通知窗口
                </div>
                <div class="text-sm text-base-content-secondary mt-1">过期多长时间后停止通知</div>
              </div>
              <select
                v-model="settingsStore.todoNotificationExpiredWindow"
                class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info focus:border-info"
              >
                <option :value="1">1分钟</option>
                <option :value="5">5分钟</option>
                <option :value="10">10分钟</option>
                <option :value="15">15分钟</option>
                <option :value="30">30分钟</option>
              </select>
            </div>

            <!-- 提前通知时间点 -->
            <div class="px-6 py-4">
              <div class="flex items-center gap-2 text-sm font-medium text-base-content mb-3">
                <AlarmClock class="w-4 h-4" />
                提前通知时间点
              </div>
              <div class="text-sm text-base-content-secondary mb-4">在到期前多长时间发送提醒</div>
              <div class="grid grid-cols-3 gap-3">
                <div
                  v-for="minute in [2, 5, 15, 30, 60, 120]"
                  :key="minute"
                  class="flex items-center"
                >
                  <label class="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      class="sr-only peer"
                      :checked="settingsStore.todoNotificationBeforeMinutes.includes(minute)"
                      @change="toggleNotificationTime(minute)"
                    />
                    <div
                      class="w-4 h-4 border-2 border-base-border rounded peer-checked:bg-info peer-checked:border-info flex items-center justify-center"
                    >
                      <svg
                        v-if="settingsStore.todoNotificationBeforeMinutes.includes(minute)"
                        class="w-2.5 h-2.5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    </div>
                    <span class="ml-2 text-sm text-base-content">{{ formatMinutes(minute) }}</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 重复通知间隔 -->
            <div class="px-6 py-4">
              <div class="flex items-center gap-2 text-sm font-medium text-base-content mb-3">
                <Repeat class="w-4 h-4" />
                重复通知间隔
              </div>
              <div class="text-sm text-base-content-secondary mb-4">
                不同状态的待办事项重复提醒的间隔
              </div>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">即将到期:</span>
                  <select
                    v-model="settingsStore.todoNotificationRepeatHours.upcoming"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info focus:border-info"
                  >
                    <option :value="0.5">30分钟</option>
                    <option :value="1">1小时</option>
                    <option :value="2">2小时</option>
                    <option :value="4">4小时</option>
                  </select>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">今日到期:</span>
                  <select
                    v-model="settingsStore.todoNotificationRepeatHours.today"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info focus:border-info"
                  >
                    <option :value="4">4小时</option>
                    <option :value="6">6小时</option>
                    <option :value="12">12小时</option>
                    <option :value="24">24小时</option>
                  </select>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">已过期:</span>
                  <select
                    v-model="settingsStore.todoNotificationRepeatHours.expired"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info focus:border-info"
                  >
                    <option :value="2">2小时</option>
                    <option :value="4">4小时</option>
                    <option :value="6">6小时</option>
                    <option :value="12">12小时</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 备份设置 -->
        <section class="rounded-lg border border-base-border overflow-hidden">
          <div class="px-6 py-4 border-b border-base-border">
            <h2 class="text-lg font-medium text-title flex items-center gap-2">
              <Database class="w-5 h-5" />
              备份设置
            </h2>
          </div>

          <div class="divide-y divide-base-200">
            <!-- 笔记备份控制 -->
            <div class="px-6 py-4">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <div class="flex items-center gap-2 text-sm font-medium text-base-content">
                    <Save class="w-4 h-4" />
                    笔记备份
                  </div>
                  <div class="text-sm text-base-content-secondary mt-1">管理笔记数据的备份</div>
                </div>
                <div class="flex gap-2">
                  <button
                    @click="toggleBackupHistory"
                    class="px-3 py-1.5 text-sm rounded-md bg-base-100 hover:bg-base-200 text-base-content transition-colors flex items-center gap-1"
                  >
                    <ChevronUp v-if="showBackupHistory" class="w-4 h-4" />
                    <ChevronDown v-else class="w-4 h-4" />
                    {{ showBackupHistory ? '隐藏历史' : '查看历史' }}
                  </button>
                  <button
                    @click="backupNow"
                    class="px-3 py-1.5 text-sm rounded-md bg-info text-white hover:bg-info/90 transition-colors flex items-center gap-1"
                  >
                    <Plus class="w-4 h-4" />
                    立即备份
                  </button>
                </div>
              </div>

              <!-- 备份历史列表 -->
              <div
                v-if="showBackupHistory"
                class="mt-4 p-4 bg-base-100 rounded-lg border border-base-border"
              >
                <BackupList ref="backupListRef" />
              </div>

              <!-- 备份设置 -->
              <div v-if="!showBackupHistory" class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">最大备份数量:</span>
                  <select
                    v-model="settingsStore.maxBackups"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info/30 focus:border-info outline-none"
                    @change="handleBackupSettingsChange"
                  >
                    <option :value="3">3个</option>
                    <option :value="5">5个</option>
                    <option :value="10">10个</option>
                  </select>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">备份间隔时间:</span>
                  <select
                    v-model="settingsStore.backupIntervalDays"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info/30 focus:border-info outline-none"
                    @change="handleBackupSettingsChange"
                  >
                    <option :value="1">1天</option>
                    <option :value="3">3天</option>
                    <option :value="7">7天</option>
                    <option :value="14">14天</option>
                    <option :value="30">30天</option>
                  </select>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content">检查间隔时间:</span>
                  <select
                    v-model="settingsStore.pollingIntervalMinutes"
                    class="px-3 py-1.5 text-sm text-base-content border border-base-border rounded-md bg-base-100 focus:ring-2 focus:ring-info/30 focus:border-info outline-none"
                    @change="handleBackupSettingsChange"
                  >
                    <option :value="15">15分钟</option>
                    <option :value="30">30分钟</option>
                    <option :value="60">1小时</option>
                    <option :value="180">3小时</option>
                    <option :value="360">6小时</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>

    <!-- 快捷键编辑模态框 -->
    <KeyboardShortcutModal
      v-if="selectedShortcut"
      :is-open="isShortcutModalOpen"
      :shortcut="selectedShortcut"
      @close="closeShortcutModal"
      @save="saveShortcut"
    />

    <!-- 微信二维码 Modal -->
    <div
      v-if="isWeChatModalOpen"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      @click.self="isWeChatModalOpen = false"
    >
      <div class="rounded-lg shadow-lg max-w-xs w-full mx-4 relative">
        <button
          class="absolute right-3 top-3 p-1 rounded-full hover:bg-base-200 text-base-content-secondary hover:text-base-content"
          @click="isWeChatModalOpen = false"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
        <div class="p-6">
          <h3 class="text-lg font-semibold text-center mb-4 text-title">Chronnote 微信群</h3>
          <div class="flex justify-center mb-4">
            <img
              src="https://qiaoyangedu.oss-cn-beijing.aliyuncs.com/chronnote/WeChat.JPG"
              alt="微信群二维码"
              class="w-full h-auto object-contain rounded-lg shadow-md"
              style="max-height: 280px"
            />
          </div>
          <p class="text-center text-sm text-base-content-secondary">
            扫一扫二维码，加入微信交流群
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@renderer/stores/user'
import { useSettingsStore, KeyboardShortcut } from '@renderer/stores/settings'
import { showToast } from '@renderer/utils/toast'
import {
  Sun,
  Moon,
  LogOut,
  ArrowLeft,
  Palette,
  Monitor,
  Mail,
  RefreshCw,
  Edit,
  MessageCircle,
  Save,
  ChevronDown,
  ChevronUp,
  Plus,
  User,
  Database,
  Keyboard,
  Lock,
  Volume2,
  BellRing,
  Bell,
  Clock,
  CalendarClock,
  AlertCircle,
  Timer,
  AlarmClock,
  Repeat,
  ListChecks
} from 'lucide-vue-next'
import KeyboardShortcutModal from '@renderer/components/Settings/KeyboardShortcutModal.vue'
import { getPlatform } from '@renderer/utils/platform'
import { setCurrentResource } from '@renderer/utils/bus'
import { track } from '@renderer/utils/tracks'
import { useGlobalStore } from '@renderer/stores/globalStore'
import { useBackupService, initBackupService } from '@renderer/utils/backupService'
import BackupList from '@renderer/components/BackupList.vue'
import { usePanelStore } from '@renderer/stores/panelStore'

const router = useRouter()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const currentVersion = ref('1.0.0')
const platform = getPlatform()
const isCheckingUpdate = ref(false)
const isWeChatModalOpen = ref(false)
const showBackupHistory = ref(false)
const backupListRef = ref<InstanceType<typeof BackupList> | null>(null)
const globalStore = useGlobalStore()
const panelStore = usePanelStore()

// 快捷键设置
const selectedShortcut = ref<KeyboardShortcut | null>(null)
const isShortcutModalOpen = ref(false)

// 计算快捷键分类
const keyboardCategories = computed(() => {
  const categories = new Set<string>()
  settingsStore.keyboardShortcuts.forEach((shortcut) => {
    categories.add(shortcut.category)
  })
  return Array.from(categories)
})

// 按分类获取快捷键
const getShortcutsByCategory = (category: string) => {
  // 先按类别过滤
  const shortcuts = settingsStore.keyboardShortcuts.filter(
    (shortcut) => shortcut.category === category
  )

  // 如果是编辑器分类，按照逻辑关系排序
  if (category === '编辑器') {
    return shortcuts.sort((a, b) => {
      // 定义逻辑组及其顺序
      const logicGroups = [
        // 标题相关
        ['editor_heading1', 'editor_heading2', 'editor_heading3', 'editor_heading4'],
        // 文本格式化
        ['editor_bold', 'editor_italic', 'editor_underline'],
        // 列表相关
        ['editor_bullet_list', 'editor_ordered_list', 'editor_task_list'],
        // 块级元素
        ['editor_quote', 'editor_code_block', 'editor_image', 'editor_math'],
        // 缩进操作
        ['editor_indent', 'editor_outdent'],
        // 剪贴板操作
        ['editor_copy', 'editor_paste'],
        // 编辑器功能
        ['editor_find'],
        // AI相关功能
        ['editor_continue', 'editor_format', 'editor_image_generate']
      ]

      // 查找每个快捷键所在的组
      const getGroupIndex = (id: string) => {
        for (let i = 0; i < logicGroups.length; i++) {
          if (logicGroups[i].includes(id)) {
            return i
          }
        }
        return logicGroups.length // 如果不在任何组中，放在最后
      }

      // 查找快捷键在组内的位置
      const getPositionInGroup = (id: string) => {
        for (let i = 0; i < logicGroups.length; i++) {
          const pos = logicGroups[i].indexOf(id)
          if (pos !== -1) {
            return pos
          }
        }
        return 0 // 如果不在任何组中，放在组内第一位
      }

      // 先按组排序，再按组内位置排序
      const groupA = getGroupIndex(a.id)
      const groupB = getGroupIndex(b.id)

      if (groupA !== groupB) {
        return groupA - groupB
      }

      return getPositionInGroup(a.id) - getPositionInGroup(b.id)
    })
  } else {
    // 其他分类按名称字母排序
    return shortcuts.sort((a, b) => {
      return a.name.localeCompare(b.name, 'zh-CN')
    })
  }
}

onMounted(async () => {
  // 初始化主题设置
  settingsStore.initTheme()

  // 初始化备份服务
  initBackupService({
    maxBackups: settingsStore.maxBackups,
    backupIntervalDays: settingsStore.backupIntervalDays,
    pollingIntervalMinutes: settingsStore.pollingIntervalMinutes
  }).start()

  // 获取应用版本
  if (platform === 'electron') {
    try {
      currentVersion.value = await (window as any).electron.ipcRenderer.invoke('get-app-version')
    } catch (error) {
      console.error('获取版本信息失败:', error)
    }
  }
})

const handleLogout = async () => {
  // 清空所有localStorage数据
  localStorage.clear()

  // 清空用户信息
  userStore.clearUserInfo()

  // 重置面板状态
  panelStore.layout.splice(0, panelStore.layout.length)
  panelStore.currentPanel = null

  setCurrentResource('', '') // 清空 current_resource

  // 通知主进程进行窗口调整
  if (platform === 'electron') {
    ;(window as any).electron.ipcRenderer.send('hidden-window')
    await router.push('/login')
    ;(window as any).electron.ipcRenderer.send('logout')
    return
  }

  await router.push('/login')
}

const handleThemeChange = (mode: 'light' | 'dark' | 'system') => {
  settingsStore.setThemeMode(mode)
  track({
    action_type: 'Theme Change',
    extra1: mode
  })
}

const checkForUpdates = async () => {
  track({
    action_type: 'Check updates'
  })
  if (platform === 'electron' && !isCheckingUpdate.value) {
    isCheckingUpdate.value = true
    try {
      const hasUpdate = await (window as any).electron.ipcRenderer.invoke('check-for-updates')
      if (hasUpdate) {
        showToast('检查更新', '发现新版本，请稍后安装', 2000)
        globalStore.isUpdaterModalOpen = true
      } else {
        showToast('检查更新', '当前已是最新版本', 2000)
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      showToast('检查更新', '检查更新失败，请稍后重试', 2000)
    } finally {
      isCheckingUpdate.value = false
    }
  }
}

const openFeedback = (platform: string) => {
  if (platform === 'qq') {
    if (getPlatform() === 'electron') {
      ;(window as any).electron.ipcRenderer.send(
        'open-external-link',
        'https://qm.qq.com/q/lfUm3dxjby'
      )
    } else {
      window.open('https://qm.qq.com/q/lfUm3dxjby', '_blank')
    }
    // 复制QQ群号到剪贴板
    navigator.clipboard
      .writeText('1021486668')
      .then(() => {
        showToast('已复制', 'QQ群号已复制到剪贴板', 2000)
      })
      .catch((err) => {
        console.error('复制失败:', err)
        showToast('复制失败', '请手动复制QQ群号: 1021486668', 2000)
      })
    track({
      action_type: 'Contact',
      extra1: 'qq'
    })
  } else if (platform === 'wechat') {
    // 打开微信二维码 modal
    isWeChatModalOpen.value = true
    track({
      action_type: 'Contact',
      extra1: 'wechat'
    })
  }
}

const handleBackupSettingsChange = () => {
  const backupService = useBackupService()
  backupService.updateSettings({
    maxBackups: settingsStore.maxBackups,
    backupIntervalDays: settingsStore.backupIntervalDays,
    pollingIntervalMinutes: settingsStore.pollingIntervalMinutes
  })
  track({
    action_type: 'Backup Settings Change'
  })
}

const backupNow = async () => {
  try {
    await useBackupService().forceCreateBackup()
    showToast('备份', '笔记备份成功', 2000)
    track({
      action_type: 'Manual Backup'
    })
  } catch (error) {
    console.error('备份失败:', error)
    showToast('备份', '笔记备份失败，请稍后重试', 2000)
  }
}

// 切换显示备份历史
const toggleBackupHistory = () => {
  showBackupHistory.value = !showBackupHistory.value

  // 如果显示备份历史，刷新备份列表
  if (showBackupHistory.value && backupListRef.value) {
    backupListRef.value.loadBackups()
  }

  track({
    action_type: 'Toggle Backup History',
    extra1: showBackupHistory.value ? 'show' : 'hide'
  })
}

// 格式化快捷键显示
const formatShortcutKeys = (keys: string[]) => {
  const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
  return keys.map((key) => {
    if (key === 'Mod') return isMac ? '⌘' : 'Ctrl'
    if (key === 'Alt') return isMac ? '⌥' : 'Alt'
    if (key === 'Shift') return isMac ? '⇧' : 'Shift'
    if (key === 'ArrowUp') return '↑'
    if (key === 'ArrowDown') return '↓'
    if (key === 'ArrowLeft') return '←'
    if (key === 'ArrowRight') return '→'
    if (key === 'Space') return 'Space'
    return key
  })
}

// 格式化分钟显示
const formatMinutes = (minutes: number) => {
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60)
    return `${hours}小时`
  }
  return `${minutes}分钟`
}

// 打开快捷键编辑模态框
const openShortcutModal = (shortcut: KeyboardShortcut) => {
  // 即使在表格中阻止了点击，这里也再次检查一下
  if (shortcut.readonly) {
    showToast('快捷键', '此快捷键为系统预设，不可修改', 2000)
    return
  }

  selectedShortcut.value = shortcut
  isShortcutModalOpen.value = true
  track({
    action_type: 'Open Shortcut Modal',
    extra1: shortcut.id
  })
}

// 关闭快捷键编辑模态框
const closeShortcutModal = () => {
  isShortcutModalOpen.value = false
  setTimeout(() => {
    selectedShortcut.value = null
  }, 300)
}

// 保存快捷键设置
const saveShortcut = (id: string, keys: string[]) => {
  settingsStore.updateKeyboardShortcut(id, keys)
  showToast('快捷键', '快捷键设置已更新', 2000)
  track({
    action_type: 'Save Shortcut',
    extra1: id
  })
}

// 切换通知时间点
const toggleNotificationTime = (minute: number) => {
  const currentTimes = [...settingsStore.todoNotificationBeforeMinutes]
  const index = currentTimes.indexOf(minute)

  if (index >= 0) {
    // 如果已存在且不是最后一个时间点，则移除
    if (currentTimes.length > 1) {
      currentTimes.splice(index, 1)
    } else {
      showToast('通知设置', '至少需要保留一个通知时间点', 2000)
      return
    }
  } else {
    // 如果不存在，则添加并排序
    currentTimes.push(minute)
    currentTimes.sort((a, b) => a - b)
  }

  settingsStore.todoNotificationBeforeMinutes = currentTimes
}

defineOptions({
  name: 'Settings'
})
</script>

<style scoped>
.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.animate-gradient-reverse {
  background-size: 200% 200%;
  animation: gradient-reverse 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-reverse {
  0% {
    background-position: 100% 50%;
  }

  50% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 100% 50%;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Add scroll padding to ensure content isn't hidden behind fixed elements */
.setting {
  scroll-padding-bottom: 2rem;
  /* Prevent layout shifts when scrollbar appears */
  scrollbar-gutter: stable;
}

/* Firefox styles */
.setting {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.setting:hover {
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
}
</style>
