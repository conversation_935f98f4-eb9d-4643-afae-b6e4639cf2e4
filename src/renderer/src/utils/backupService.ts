import { useUserStore } from '@renderer/stores/user'
import { FileSystemManager } from '../api/uniform/fs'
import { useConfig } from './configHelper'
import { ref } from 'vue'
import { DatabaseManager } from '../api/uniform/db'
import { isDirectory } from './fsUtils'

// Default backup settings
const DEFAULT_BACKUP_SETTINGS = {
  maxBackups: 5,
  backupIntervalDays: 1,
  pollingIntervalMinutes: 30
}

interface BackupSettings {
  maxBackups: number
  backupIntervalDays: number
  pollingIntervalMinutes: number
}

export class BackupService {
  private fs: FileSystemManager
  private timer: NodeJS.Timeout | null = null
  private settings: BackupSettings
  private isRunning: boolean = false

  constructor(settings?: Partial<BackupSettings>) {
    this.fs = new FileSystemManager()
    this.settings = {
      ...DEFAULT_BACKUP_SETTINGS,
      ...settings
    }
  }

  /**
   * 获取当前用户的备份目录路径
   */
  private getCurrentUserBackupPath(): string {
    const config = useConfig()
    const userId = useUserStore().userInfo?.uuid || 'default'
    return config.getUserBackupPath(userId)
  }

  /**
   * 删除用户数据，但保留backup目录以保护历史备份
   */
  private async removeUserDataExcludingBackup(userWorkspace: string): Promise<void> {
    try {
      // 读取用户工作空间中的所有项目
      const items = await this.fs.readdir(userWorkspace)
      
      for (const item of items) {
        // 跳过backup目录，保护历史备份
        if (item === 'backup') {
          continue
        }
        
        const itemPath = `${userWorkspace}/${item}`
        
        try {
          // 删除非backup目录的所有内容
          await this.fs.remove(itemPath)
        } catch (itemError) {
          console.warn(`Failed to remove item ${item}:`, itemError)
          // 继续处理其他项目，不因单个文件删除失败而中断整个恢复
        }
      }
    } catch (error) {
      console.error('Failed to remove user data:', error)
      throw error
    }
  }

  /**
   * 复制用户数据，但排除backup目录以避免循环依赖
   */
  private async copyUserDataExcludingBackup(sourcePath: string, targetPath: string): Promise<void> {
    try {
      // 读取源目录中的所有项目
      const items = await this.fs.readdir(sourcePath)
      
      for (const item of items) {
        // 跳过backup目录，避免循环依赖
        if (item === 'backup') {
          continue
        }
        
        const sourceItemPath = `${sourcePath}/${item}`
        const targetItemPath = `${targetPath}/${item}`
        
        try {
          // 检查是否是目录，使用跨平台兼容的isDirectory函数
          const stats = await this.fs.stat(sourceItemPath)
          if (isDirectory(stats)) {
            // 如果是目录，递归复制
            await this.fs.mkdir(targetItemPath)
            await this.copyUserDataExcludingBackup(sourceItemPath, targetItemPath)
          } else {
            // 如果是文件，直接复制
            await this.fs.copyFile(sourceItemPath, targetItemPath)
          }
        } catch (itemError) {
          console.warn(`Failed to copy item ${item}:`, itemError)
          // 继续处理其他项目，不因单个文件失败而中断整个备份
        }
      }
    } catch (error) {
      console.error('Failed to copy user data:', error)
      throw error
    }
  }

  /**
   * 确保备份目录存在
   */
  private async ensureBackupDirExists(): Promise<void> {
    try {
      const backupPath = this.getCurrentUserBackupPath()
      // 检查备份目录是否存在，如果不存在则创建
      try {
        await this.fs.stat(backupPath)
      } catch (error) {
        await this.fs.mkdir(backupPath)
      }
    } catch (error) {
      console.error('Failed to ensure backup directory exists:', error)
    }
  }

  /**
   * Start the backup polling service
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    this.isRunning = true

    // 确保备份目录存在
    await this.ensureBackupDirExists()

    // 启动时检查并清理超出限制的备份
    try {
      const backups = await this.listBackups()
      if (backups.length > this.settings.maxBackups) {
        // 按时间从旧到新排序
        backups.sort((a, b) => a.createdAt - b.createdAt)

        // 计算需要删除的备份数量
        const deleteCount = backups.length - this.settings.maxBackups

        // 删除最旧的备份
        for (let i = 0; i < deleteCount; i++) {
          await this.fs.remove(backups[i].path)
        }
      }
    } catch (error) {
      console.error('Failed to clean up excess backups on startup:', error)
    }

    // 启动时不立即创建备份，而是检查是否需要创建
    setTimeout(() => {
      // 使用setTimeout稍微延迟检查，确保应用完全启动
      this.checkAndCreateBackup()
    }, 5000) // 5秒后检查，避免应用刚启动就进行备份

    // 设置定期检查备份的间隔
    const intervalMs = this.settings.pollingIntervalMinutes * 60 * 1000

    this.timer = setInterval(() => {
      this.checkAndCreateBackup()
    }, intervalMs)
  }

  /**
   * Stop the backup polling service
   */
  public stop(): void {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    this.isRunning = false
  }

  /**
   * Update backup settings
   */
  public updateSettings(settings: Partial<BackupSettings>): void {
    this.settings = {
      ...this.settings,
      ...settings
    }

    // Restart the service if it's running
    if (this.isRunning) {
      this.stop()
      this.start()
    }
  }

  /**
   * Check and create a backup if needed
   */
  public async checkAndCreateBackup(): Promise<void> {
    try {
      // 确保备份目录存在
      await this.ensureBackupDirExists()

      // Get all backup directories
      const backups = await this.listBackups()

      if (backups.length === 0) {
        // No backups yet, create the first one
        await this.createBackup()
        return
      }

      // Sort by creation time (newest first)
      backups.sort((a, b) => b.createdAt - a.createdAt)
      const newestBackup = backups[0]
      const now = Date.now()
      const daysSinceLastBackup = (now - newestBackup.createdAt) / (1000 * 60 * 60 * 24)

      if (daysSinceLastBackup < this.settings.backupIntervalDays) {
        // 最新的备份还没有超过指定的时间间隔，不需要创建新备份

        return
      }

      // 如果代码执行到这里，说明已经超过了备份间隔，需要创建新备份

      // 检查是否已达到最大备份数
      if (backups.length >= this.settings.maxBackups) {
        // 我们已经达到了最大备份数，需要删除最旧的备份
        // Sort by creation time (oldest first)
        backups.sort((a, b) => a.createdAt - b.createdAt)

        // Delete the oldest backup
        const oldestBackup = backups[0]

        await this.fs.remove(oldestBackup.path)
      }

      // 创建新备份
      await this.createBackup()
    } catch (error) {
      console.error('Error checking and creating backup:', error)
    }
  }

  /**
   * Create a new database backup
   */
  private async createBackup(): Promise<void> {
    try {
      // Get config
      const config = useConfig()
      const userId = useUserStore().userInfo?.uuid || 'default'

      // Create timestamp for backup folder
      const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '')

      // Create a new directory for this backup
      const userBackupPath = this.getCurrentUserBackupPath()
      const backupDir = `${userBackupPath}/${timestamp}`

      try {
        await this.fs.mkdir(backupDir)
      } catch (error) {
        console.error(`Failed to create backup directory: ${backupDir}`, error)
        throw new Error(`Failed to create backup directory: ${error}`)
      }

      try {
        // 新架构：直接将用户内容备份到日期目录中，排除backup子目录避免循环依赖
        const userWorkspace = await config.getUserWorkspace(userId)
        
        await this.copyUserDataExcludingBackup(userWorkspace, backupDir)
      } catch (error) {
        console.error('Error copying example directory:', error)
        // 尝试清理失败的备份目录
        try {
          await this.fs.remove(backupDir)
        } catch (cleanupError) {
          console.error(`Failed to clean up backup directory: ${backupDir}`, cleanupError)
        }
        throw error
      }
    } catch (error) {
      console.error('Error creating backup:', error)
      throw error
    }
  }

  /**
   * Force create a backup regardless of time interval
   * Used for manual backup from settings page
   */
  public async forceCreateBackup(): Promise<void> {
    try {
      const userBackupPath = this.getCurrentUserBackupPath()
      // Get all backup directories in backup directory (they are timestamped folders)
      const allDirs = await this.fs.readdir(userBackupPath)
      // Filter out any files that might be in the backup directory
      const backupDirs = await Promise.all(
        allDirs.map(async (dir) => {
          const dirPath = `${userBackupPath}/${dir}`
          try {
            const stats = await this.fs.stat(dirPath)
            return { path: dirPath, isDir: isDirectory(stats), name: dir }
          } catch {
            return { path: dirPath, isDir: false, name: dir }
          }
        })
      ).then((results) => results.filter((result) => result.isDir))

      // If we already have max backups, remove the oldest one
      if (backupDirs.length >= this.settings.maxBackups) {
        // Sort directories by name (timestamp) - oldest first
        backupDirs.sort((a, b) => a.name.localeCompare(b.name))

        // Delete the oldest backup
        const oldestBackup = backupDirs[0]
        await this.fs.remove(oldestBackup.path)
      }

      // Create a new backup
      await this.createBackup()
    } catch (error) {
      console.error('Error creating manual backup:', error)
      throw error
    }
  }

  /**
   * List all available backups
   */
  public async listBackups(): Promise<Array<{ name: string; path: string; createdAt: number }>> {
    try {
      const userBackupPath = this.getCurrentUserBackupPath()
      // 先确保备份目录存在
      try {
        await this.fs.stat(userBackupPath)
      } catch (error) {
        await this.fs.mkdir(userBackupPath)
        return [] // 目录刚创建，肯定没有备份
      }

      // Get all directories in backup directory
      const allItems = await this.fs.readdir(userBackupPath)

      if (allItems.length === 0) {
        return []
      }

      // Filter and get stats for directories only
      const backupDirs = await Promise.all(
        allItems.map(async (item) => {
          const itemPath = `${userBackupPath}/${item}`
          try {
            // 通过主进程的 fs:isDirectory 方法检查是否是目录
            const result = await window.electron.ipcRenderer.invoke('fs:isDirectory', itemPath)

            if (result.success && result.isDirectory) {
              // 尽可能获取有效的时间戳
              let createdTime: number = Date.now() // 默认值

              // 获取文件状态以获取时间戳
              const stats = await this.fs.stat(itemPath)

              // 尝试获取创建时间
              if (typeof stats.birthtimeMs === 'number') {
                createdTime = stats.birthtimeMs
              } else if (typeof stats.ctimeMs === 'number') {
                createdTime = stats.ctimeMs
              } else if (stats.birthtime instanceof Date) {
                createdTime = stats.birthtime.getTime()
              } else if (stats.ctime instanceof Date) {
                createdTime = stats.ctime.getTime()
              } else if (stats.mtime instanceof Date) {
                createdTime = stats.mtime.getTime()
              } else if (typeof stats.mtime === 'number') {
                createdTime = stats.mtime
              } else {
                // 如果无法获取时间戳，尝试从备份目录名称中提取（如果名称是ISO日期格式）
                try {
                  // 尝试解析目录名（假设它是ISO日期格式）
                  const date = new Date(item.replace(/-/g, ':'))
                  if (!isNaN(date.getTime())) {
                    createdTime = date.getTime()
                  }
                } catch (e) {
                  console.log(
                    `Could not parse date from directory name: ${item}, using current time`
                  )
                }
              }

              return {
                name: item,
                path: itemPath,
                createdAt: createdTime
              }
            }
            return null
          } catch (error) {
            console.error(`Error checking item ${item}:`, error)
            return null
          }
        })
      ).then((results) =>
        results.filter(
          (result): result is { name: string; path: string; createdAt: number } => result !== null
        )
      )

      if (backupDirs.length === 0) {
        return []
      }

      // Sort by creation time (newest first for display)
      backupDirs.sort((a, b) => b.createdAt - a.createdAt)

      // Log the newest backup and its age
      if (backupDirs.length > 0) {
        const newest = backupDirs[0]
        const now = Date.now()
        const daysSinceNewest = (now - newest.createdAt) / (1000 * 60 * 60 * 24)
      }

      return backupDirs
    } catch (error) {
      console.error('Error listing backups:', error)
      throw error
    }
  }

  /**
   * Restore a backup from the given backup directory path
   * @param backupDirPath Path to the backup directory
   */
  public async restoreBackup(backupDirPath: string): Promise<void> {
    let dbInstance: DatabaseManager | null = null

    try {
      // Get current workspace path
      const config = useConfig()
      const userId = useUserStore().userInfo?.uuid || 'default'
      const userWorkspace = await config.getUserWorkspace(userId)
      const currentDbPath = await config.getUserDbPath(userId)

      // Create a backup of current state before restoring
      await this.forceCreateBackup()

      // 关闭当前数据库连接（如果有）
      try {
        // 尝试创建一个临时数据库实例，用于关闭当前连接
        dbInstance = new DatabaseManager(currentDbPath)
        await dbInstance.close()
      } catch (err) {
        console.warn('Failed to close database connection, might not be open', err)
      }

      // 确保有足够的时间完全关闭数据库连接
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 保护性删除：删除用户工作空间内容但保留backup目录
      await this.removeUserDataExcludingBackup(userWorkspace)

      // 恢复用户专属备份，现在备份内容直接在备份目录中
      await this.fs.copy(backupDirPath, userWorkspace)
    } catch (error) {
      console.error('Error restoring backup:', error)
      throw error
    }
  }

  /**
   * Delete a backup directory
   * @param backupDirPath Path to the backup directory to delete
   */
  public async deleteBackup(backupDirPath: string): Promise<void> {
    try {
      // Check if directory exists
      try {
        const result = await window.electron.ipcRenderer.invoke('fs:isDirectory', backupDirPath)
        if (!result.success || !result.isDirectory) {
          throw new Error('指定的备份路径不是一个目录')
        }
      } catch (error) {
        console.error('Backup directory does not exist, cannot delete', error)
        throw new Error('备份目录不存在')
      }

      // Delete the directory and all its contents
      await this.fs.remove(backupDirPath)
    } catch (error) {
      console.error('Error deleting backup:', error)
      throw error
    }
  }
}

// Create a singleton instance
const backupServiceInstance = ref<BackupService | null>(null)

/**
 * Initialize the backup service
 */
export function initBackupService(settings?: Partial<BackupSettings>): BackupService {
  if (!backupServiceInstance.value) {
    backupServiceInstance.value = new BackupService(settings)
  } else if (settings) {
    backupServiceInstance.value.updateSettings(settings)
  }

  // 使用类型断言确保返回类型正确
  return backupServiceInstance.value as BackupService
}

/**
 * Get the backup service instance
 */
export function useBackupService(): BackupService {
  if (!backupServiceInstance.value) {
    throw new Error('Backup service not initialized. Please call initBackupService first')
  }

  // 使用类型断言确保返回类型正确
  return backupServiceInstance.value as BackupService
}

export default {
  initBackupService,
  useBackupService
}
