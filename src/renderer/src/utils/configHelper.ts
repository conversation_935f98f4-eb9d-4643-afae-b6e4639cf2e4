import { FileSystemManager } from '../api/uniform/fs'
import { ref } from 'vue'
import { getPlatform, Platform } from './platform'
import { useStorageCompatibilityAdapter } from './storageCompatibilityAdapter'

// Define interfaces for platform-specific path getters
interface IPathProvider {
  getAppDataPath(): Promise<string>
  getCwd(): Promise<string>
}

// Electron implementation
class ElectronPathProvider implements IPathProvider {
  async getAppDataPath(): Promise<string> {
    return await window.electron.getPath('userData')
  }

  async getCwd(): Promise<string> {
    return await window.electron.getCwd()
  }
}

// Web implementation
class WebPathProvider implements IPathProvider {
  async getAppDataPath(): Promise<string> {
    // For web, we'll use localStorage or IndexedDB path
    return '/app-data'
  }

  async getCwd(): Promise<string> {
    // For web, we'll use a default path
    return '/workspace'
  }
}

// TODO: iOS implementation
class IOSPathProvider implements IPathProvider {
  async getAppDataPath(): Promise<string> {
    throw new Error('iOS implementation not yet available')
  }

  async getCwd(): Promise<string> {
    throw new Error('iOS implementation not yet available')
  }
}

class ConfigHelper {
  mode: 'development' | 'production'
  demoPath: string = '' // 保留用于兼容性
  rootPath: string = ''
  workspacePath: string = ''
  backupPath: string = ''
  resourcesPath: string = ''
  // 移除旧的路径配置：flowPath, chatPath, imgPath, pdfPath
  // 这些现在通过用户专属方法获取
  private fs: FileSystemManager
  private platform: Platform
  private pathProvider: IPathProvider

  private constructor() {
    this.fs = new FileSystemManager()
    this.mode = import.meta.env.DEV ? 'development' : 'production'
    this.platform = getPlatform()

    // Initialize the appropriate path provider based on platform
    switch (this.platform) {
      case 'electron':
        this.pathProvider = new ElectronPathProvider()
        break
      case 'web':
        this.pathProvider = new WebPathProvider()
        break
      case 'ios':
        this.pathProvider = new IOSPathProvider()
        break
      default:
        throw new Error(`Unsupported platform: ${this.platform}`)
    }
  }

  static async create(): Promise<ConfigHelper> {
    const instance = new ConfigHelper()
    await instance.initialize()
    return instance
  }

  private async initialize() {
    try {
      // Get base paths based on platform
      const appDataPath = await this.pathProvider.getAppDataPath()
      const cwd = await this.pathProvider.getCwd()

      // Set base path according to mode and platform
      let basePath: string
      if (this.platform === 'web') {
        basePath = this.mode === 'development' ? '/dev-workspace' : '/prod-workspace'
      } else {
        basePath = this.mode === 'development' ? cwd : appDataPath
      }

      // Initialize paths - 新的用户隔离架构
      this.demoPath = `${basePath}/example/note` // 保留用于兼容性
      this.rootPath = this.mode === 'development' ? basePath : `${appDataPath}/example`
      this.backupPath = `${basePath}/backup`
      this.workspacePath = `${basePath}/example` // 保持指向example目录，用户数据存储在example/users/

      await this.init()
    } catch (error) {
      console.error('Failed to initialize config:', error)
      throw error
    }
  }

  private async init() {
    if (this.platform === 'web') {
      // For web platform, we might want to initialize storage or other web-specific setup
      await this.initializeWebStorage()
      return
    }

    // For electron (and future iOS support), create base directories
    // 注意：不再创建 demoPath (example/note)，因为新架构使用用户专属目录
    await this.fs.mkdir(this.workspacePath)
    await this.fs.mkdir(this.rootPath)
    await this.fs.mkdir(this.backupPath)

    // 注意：用户专属目录（img, pdf等）现在在用户登录时创建
    // 参见 InitializationService.ensureUserDirectories()

    // Production mode initialization for electron
    if (this.mode === 'production' && this.platform === 'electron') {
      await this.initializeProductionFiles()
    }
  }

  private async initializeWebStorage() {
    // Initialize web storage structure
    if (!localStorage.getItem('flowJson')) {
      localStorage.setItem('flowJson', '{}')
    }
    // Additional web-specific initialization can be added here
  }

  private async initializeProductionFiles() {
    try {
      await this.fs.stat(this.rootPath)
    } catch {
      await this.fs.mkdir(`${this.rootPath}/example`)
      await this.copyExampleFiles()
    }

    try {
      await this.fs.stat(this.backupPath)
    } catch {
      await this.fs.mkdir(this.backupPath)
      await this.copyBackupFiles()
    }
  }

  private async copyExampleFiles() {
    try {
      const files = await this.fs.readdir(`${this.resourcesPath}/example`)
      for (const item of files) {
        await this.fs.copyFile(
          `${this.resourcesPath}/example/${item}`,
          `${this.rootPath}/example/${item}`
        )
      }
    } catch (error) {
      console.error('Failed to copy example files:', error)
    }
  }

  private async copyBackupFiles() {
    try {
      // 从example目录复制文件到backup目录，作为备份基准
      const files = await this.fs.readdir(`${this.workspacePath}`)
      for (const item of files) {
        // 只复制必要的基础文件，而不是数据库文件
        if (!item.endsWith('.db')) {
          await this.fs.copyFile(`${this.workspacePath}/${item}`, `${this.backupPath}/${item}`)
        }
      }
    } catch (error) {
      console.error('Failed to copy backup files:', error)
    }
  }

  // ===== 兼容性适配的用户专属路径方法 =====

  /**
   * 获取用户专属工作空间目录（兼容新旧架构）
   * @param userId 用户UUID
   * @returns 用户工作空间路径
   */
  async getUserWorkspace(userId: string): Promise<string> {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.getAdaptiveWorkspace(userId)
  }

  /**
   * 获取用户专属数据库路径（不包含.db扩展名，兼容新旧架构）
   * @param userId 用户UUID
   * @returns 数据库路径
   */
  async getUserDbPath(userId: string): Promise<string> {
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.getAdaptiveDbPath(userId)
  }

  /**
   * 获取用户专属图片目录（兼容新旧架构）
   * @param userId 用户UUID
   * @returns 图片目录路径
   */
  async getUserImgPath(userId: string): Promise<string> {
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.getAdaptiveImgPath(userId)
  }

  /**
   * 获取用户专属PDF目录（兼容新旧架构）
   * @param userId 用户UUID
   * @returns PDF目录路径
   */
  async getUserPdfPath(userId: string): Promise<string> {
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.getAdaptivePdfPath(userId)
  }

  /**
   * 获取用户专属blocks文件路径（兼容新旧架构）
   * @param userId 用户UUID
   * @returns blocks.json文件路径
   */
  async getUserBlocksPath(userId: string): Promise<string> {
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.getAdaptiveBlocksPath(userId)
  }

  // ===== 新架构专用方法（向后兼容） =====

  /**
   * 获取新架构的用户工作空间目录
   * @param userId 用户UUID
   * @returns 新架构用户工作空间路径
   */
  getModernUserWorkspace(userId: string): string {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    return `${this.workspacePath}/users/${userId}`
  }

  /**
   * 获取新架构的用户数据库路径
   * @param userId 用户UUID
   * @returns 新架构数据库路径
   */
  getModernUserDbPath(userId: string): string {
    return `${this.getModernUserWorkspace(userId)}/chron`
  }

  /**
   * 获取新架构的用户图片目录
   * @param userId 用户UUID
   * @returns 新架构图片目录路径
   */
  getModernUserImgPath(userId: string): string {
    return `${this.getModernUserWorkspace(userId)}/img`
  }

  /**
   * 获取新架构的用户PDF目录
   * @param userId 用户UUID
   * @returns 新架构PDF目录路径
   */
  getModernUserPdfPath(userId: string): string {
    return `${this.getModernUserWorkspace(userId)}/pdf`
  }

  /**
   * 获取用户专属备份目录
   * @param userId 用户UUID
   * @returns 用户备份目录路径
   */
  getUserBackupPath(userId: string): string {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    return `${this.getModernUserWorkspace(userId)}/backup`
  }

  /**
   * 为用户创建完整的目录结构（新架构）
   * @param userId 用户UUID
   */
  async createUserDirectories(userId: string): Promise<void> {
    try {
      const userWorkspace = this.getModernUserWorkspace(userId)
      const imgPath = this.getModernUserImgPath(userId)
      const pdfPath = this.getModernUserPdfPath(userId)
      const backupPath = this.getUserBackupPath(userId)

      // 创建用户工作空间和资源目录
      await this.fs.mkdir(userWorkspace)
      await this.fs.mkdir(imgPath)
      await this.fs.mkdir(pdfPath)
      await this.fs.mkdir(backupPath)

      console.log(`为用户 ${userId} 创建新架构目录结构完成`)
    } catch (error) {
      console.error(`为用户 ${userId} 创建目录结构失败:`, error)
      throw error
    }
  }

  /**
   * 检查用户目录是否存在（兼容新旧架构）
   * @param userId 用户UUID
   * @returns 是否存在完整的用户目录结构
   */
  async checkUserDirectories(userId: string): Promise<boolean> {
    try {
      const adapter = useStorageCompatibilityAdapter()
      const validation = await adapter.validateStorageIntegrity(userId)
      return validation.isValid
    } catch (error) {
      return false
    }
  }

  /**
   * 获取用户的存储模式
   * @param userId 用户UUID
   * @returns 存储模式：'modern' 或 'legacy'
   */
  async getUserStorageMode(userId: string): Promise<'modern' | 'legacy'> {
    const adapter = useStorageCompatibilityAdapter()
    return await adapter.detectStorageMode(userId)
  }
}

// Create reactive config instance
const configInstance = ref<ConfigHelper | null>(null)
let initPromise: Promise<ConfigHelper> | null = null

export async function initConfig() {
  if (!initPromise) {
    initPromise = ConfigHelper.create()
    configInstance.value = await initPromise
  }
  return configInstance.value
}

export function useConfig() {
  if (!configInstance.value) {
    throw new Error('Config not initialized. Please call initConfig first')
  }
  return configInstance.value
}

export default {
  initConfig,
  useConfig
}
