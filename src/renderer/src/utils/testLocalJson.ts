// Simple test for useLocal<PERSON>son functionality
import { useLocal<PERSON>son } from '../composables/useLocalJson'

// Test the KV storage system
export function testLocalJsonStorage() {
  console.log('Testing useLocalJson storage system...')

  // Create test stores
  const userSettings = useLocalJson('userSettings')
  const todoStore = useLocalJson('todos')

  // Test sequential operations
  console.log('Setting initial values...')

  // Set values using the new sync API
  userSettings.set('theme', 'dark')
  userSettings.set('language', 'en')

  todoStore.set('item1', { text: 'Learn Vue', done: false })
  todoStore.set('item2', { text: 'Build KV system', done: true })
  todoStore.set('count', 42)

  console.log('Retrieving values...')

  // Test get operations with new sync API
  const theme = userSettings.get<string>('theme')
  const language = userSettings.get<string>('language')
  const item1 = todoStore.get<any>('item1')
  const count = todoStore.get<number>('count')

  console.log('Retrieved values:')
  console.log('Theme:', theme)
  console.log('Language:', language)
  console.log('Item1:', item1)
  console.log('Count:', count)

  // Test keys() method
  const userSettingsKeys = userSettings.keys()
  const todoStoreKeys = todoStore.keys()
  console.log('UserSettings keys:', userSettingsKeys)
  console.log('TodoStore keys:', todoStoreKeys)

  // Test has() method
  const hasTheme = userSettings.has('theme')
  const hasNonexistent = userSettings.has('nonexistent')
  console.log('Has theme:', hasTheme)
  console.log('Has nonexistent:', hasNonexistent)

  // Test remove() method
  console.log('Removing count...')
  todoStore.remove('count')

  const keysAfterRemove = todoStore.keys()
  console.log('After removing count:', keysAfterRemove)

  // Test directory creation by creating a new store
  const newStore = useLocalJson('testDirectoryCreation')
  newStore.set('test', 'Directory should be created automatically')

  const newStoreTest = newStore.get('test')
  console.log('New store test:', newStoreTest)

  // Test concurrent operations (should be handled gracefully)
  console.log('Testing concurrent operations...')
  const concurrentStore = useLocalJson('concurrent')

  // Fire multiple concurrent operations
  for (let i = 0; i < 10; i++) {
    concurrentStore.set(`key${i}`, `value${i}`)
  }

  const concurrentKeys = concurrentStore.keys()
  console.log('Concurrent keys count:', concurrentKeys.length)

  // Test clear operation
  console.log('Testing clear operation...')
  concurrentStore.clear()
  const keysAfterClear = concurrentStore.keys()
  console.log('Keys after clear:', keysAfterClear)

  // Test rapid successive operations
  console.log('Testing rapid successive operations...')
  const rapidStore = useLocalJson('rapid')

  for (let i = 0; i < 5; i++) {
    rapidStore.set(`rapid${i}`, `value${i}`)
  }

  const rapidKeys = rapidStore.keys()
  console.log('Rapid keys count:', rapidKeys.length)

  // Wait a bit to let file operations complete
  setTimeout(() => {
    console.log('✅ useLocalJson test completed successfully!')
    console.log('📁 Check example/users/{userId}/kv/ directory for created JSON files')
  }, 200)
}

// Export for manual testing
export { testLocalJsonStorage as default }
