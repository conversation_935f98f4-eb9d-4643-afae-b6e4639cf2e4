/**
 * 类型转换测试
 * 用于验证 Uint8Array 到字符串的转换是否正常工作
 */

// 模拟 Uint8Array 数据（JSON 格式）
const jsonString = '{"items": [{"uuid": "test-123", "title": "测试项目"}]}'
const encoder = new TextEncoder()
const uint8Array = encoder.encode(jsonString)

console.log('原始字符串:', jsonString)
console.log('Uint8Array:', uint8Array)

// 测试转换回字符串
const decoder = new TextDecoder('utf-8')
const decodedString = decoder.decode(uint8Array)

console.log('解码后的字符串:', decodedString)
console.log('转换是否成功:', decodedString === jsonString)

// 测试 JSON 解析
try {
  const parsed = JSON.parse(decodedString)
  console.log('JSON 解析成功:', parsed)
} catch (error) {
  console.error('JSON 解析失败:', error)
}

// 添加到 window 对象以便在控制台中使用
if (typeof window !== 'undefined') {
  window.testTypeConversion = () => {
    console.log('=== 类型转换测试 ===')
    console.log('原始字符串:', jsonString)
    console.log('Uint8Array:', uint8Array)
    console.log('解码后的字符串:', decodedString)
    console.log('转换是否成功:', decodedString === jsonString)
    
    try {
      const parsed = JSON.parse(decodedString)
      console.log('JSON 解析成功:', parsed)
      return true
    } catch (error) {
      console.error('JSON 解析失败:', error)
      return false
    }
  }
  
  console.log('测试函数已添加到 window.testTypeConversion')
}
