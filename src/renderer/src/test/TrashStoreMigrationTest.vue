<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">垃圾回收站迁移测试</h1>
    
    <!-- 状态显示 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="p-4 border rounded-lg">
        <h3 class="font-semibold mb-2">初始化状态</h3>
        <p class="text-sm">
          已初始化: <span :class="trashStore.isInitialized ? 'text-green-600' : 'text-red-600'">
            {{ trashStore.isInitialized ? '是' : '否' }}
          </span>
        </p>
      </div>
      
      <div class="p-4 border rounded-lg">
        <h3 class="font-semibold mb-2">垃圾回收站项目</h3>
        <p class="text-sm">
          项目数量: <span class="font-mono">{{ trashStore.trashCount }}</span>
        </p>
      </div>
      
      <div class="p-4 border rounded-lg">
        <h3 class="font-semibold mb-2">存储路径</h3>
        <p class="text-xs text-gray-600">
          example/users/:uuid/kv/trash.json
        </p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex flex-wrap gap-3 mb-6">
      <button
        @click="testInitialization"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        测试初始化
      </button>
      
      <button
        @click="addTestItem"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        添加测试项目
      </button>
      
      <button
        @click="checkLegacyData"
        class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
      >
        检查旧数据
      </button>
      
      <button
        @click="clearAllData"
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        清空所有数据
      </button>
      
      <button
        @click="refreshData"
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        刷新数据
      </button>
    </div>

    <!-- 日志显示 -->
    <div class="mb-6">
      <h3 class="font-semibold mb-2">测试日志</h3>
      <div class="bg-gray-100 p-4 rounded-lg h-40 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="text-sm mb-1">
          <span class="text-gray-500">{{ log.time }}</span>
          <span class="ml-2">{{ log.message }}</span>
        </div>
      </div>
      <button
        @click="clearLogs"
        class="mt-2 px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
      >
        清空日志
      </button>
    </div>

    <!-- 垃圾回收站项目列表 -->
    <div v-if="trashStore.trashCount > 0">
      <h3 class="font-semibold mb-3">垃圾回收站项目</h3>
      <div class="space-y-2">
        <div
          v-for="item in trashStore.sortedTrashItems"
          :key="item.uuid"
          class="p-3 border rounded-lg flex justify-between items-center"
        >
          <div>
            <p class="font-medium">{{ item.originalNode.title }}</p>
            <p class="text-sm text-gray-600">
              类型: {{ item.originalNode.type }} | 
              删除时间: {{ new Date(item.deletedAt).toLocaleString() }}
            </p>
          </div>
          <div class="flex gap-2">
            <button
              @click="restoreItem(item.uuid)"
              class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              恢复
            </button>
            <button
              @click="deleteItem(item.uuid)"
              class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              永久删除
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="text-center py-8 text-gray-500">
      垃圾回收站为空
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useTrashStore } from '../stores/trashStore'
import { UserBasedLocalStorage } from '../utils/UserBasedLocalStorage'
import { showToast } from '../utils/toast'

const trashStore = useTrashStore()

// 日志系统
const logs = ref<Array<{ time: string, message: string }>>([])

const addLog = (message: string) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message
  })
}

const clearLogs = () => {
  logs.value = []
}

// 测试初始化
const testInitialization = async () => {
  try {
    addLog('开始测试初始化...')
    await trashStore.initialize()
    addLog(`初始化完成，状态: ${trashStore.isInitialized}`)
    addLog(`垃圾回收站项目数量: ${trashStore.trashCount}`)
  } catch (error) {
    addLog(`初始化失败: ${error}`)
  }
}

// 添加测试项目
const addTestItem = async () => {
  try {
    const testNode = {
      uuid: 'test-' + Date.now(),
      title: '测试项目 ' + new Date().toLocaleTimeString(),
      type: 'note',
      children: [],
      parent: null,
      meta: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    } as any

    await trashStore.addToTrash(testNode)
    addLog(`添加测试项目: ${testNode.title}`)
    showToast('成功', '已添加测试项目', 2000)
  } catch (error) {
    addLog(`添加测试项目失败: ${error}`)
    showToast('错误', '添加测试项目失败', 3000)
  }
}

// 检查旧数据
const checkLegacyData = () => {
  try {
    const userStorage = UserBasedLocalStorage.getInstance()
    const legacyData = userStorage.getItem('chronnote_trash_items')
    
    if (legacyData) {
      const items = JSON.parse(legacyData)
      addLog(`发现旧数据: ${items.length} 个项目`)
    } else {
      addLog('未发现旧数据')
    }
  } catch (error) {
    addLog(`检查旧数据失败: ${error}`)
  }
}

// 清空所有数据
const clearAllData = async () => {
  try {
    await trashStore.clearTrash()
    addLog('已清空所有垃圾回收站数据')
    showToast('成功', '已清空所有数据', 2000)
  } catch (error) {
    addLog(`清空数据失败: ${error}`)
    showToast('错误', '清空数据失败', 3000)
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    await trashStore.initialize()
    addLog('数据已刷新')
  } catch (error) {
    addLog(`刷新数据失败: ${error}`)
  }
}

// 恢复项目
const restoreItem = async (uuid: string) => {
  try {
    const item = await trashStore.restoreItem(uuid)
    if (item) {
      addLog(`恢复项目: ${item.originalNode.title}`)
      showToast('成功', '项目已恢复', 2000)
    }
  } catch (error) {
    addLog(`恢复项目失败: ${error}`)
    showToast('错误', '恢复项目失败', 3000)
  }
}

// 删除项目
const deleteItem = async (uuid: string) => {
  try {
    const success = await trashStore.permanentlyDelete(uuid)
    if (success) {
      addLog(`永久删除项目: ${uuid}`)
      showToast('成功', '项目已永久删除', 2000)
    }
  } catch (error) {
    addLog(`删除项目失败: ${error}`)
    showToast('错误', '删除项目失败', 3000)
  }
}

// 页面加载时初始化
onMounted(async () => {
  addLog('页面加载，开始初始化测试...')
  await testInitialization()
})
</script>
