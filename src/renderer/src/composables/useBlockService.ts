import { DatabaseManager } from '@renderer/api/uniform/db'
import { getDbPath } from '@renderer/sqls/constants'
import { useUserStore } from '@renderer/stores/user'
import { resourceType } from '@renderer/componentSystem/types/resourceTypes'

export interface Block {
  uuid: string
  type: resourceType
  title: string
  createdAt: number
  updatedAt: number
  contents?: string
  meta?: string
  annotations?: string
}

const createBlockTable = `
CREATE TABLE IF NOT EXISTS Block (
    uuid TEXT PRIMARY KEY,
    type TEXT NOT NULL, -- 区分类型，不限制具体值以支持未来可能添加的新类型
    title TEXT NOT NULL,
    createdAt INTEGER NOT NULL,
    updatedAt INTEGER NOT NULL,
    contents TEXT, -- 仅当类型为 note 或 xingliu 时非空
    annotations TEXT, -- 存储 JSON 数组
    meta TEXT,     -- 存储 JSON 数组
    selection INTEGER -- 用于指向用户当前的笔记进度
);
`

class BlockService {
  private dbManager: DatabaseManager | null = null
  private initPromise: Promise<void> | null = null

  constructor() {
    this.initPromise = this.initializeDatabase()
  }

  private async initializeDatabase() {
    const userStore = useUserStore()
    if (!userStore.userInfo) {
      throw new Error('User must be logged in to initialize BlockService')
    }
    const userId = userStore.userInfo.uuid
    if (!userId) {
      throw new Error('User ID is required to initialize BlockService')
    }

    const dbPath = await getDbPath(userId)
    this.dbManager = new DatabaseManager(dbPath)
    await this.dbManager.createTable(createBlockTable)
  }

  private async ensureDbManager() {
    if (this.initPromise) {
      await this.initPromise
      this.initPromise = null
    }
    if (!this.dbManager) {
      throw new Error('Database manager not initialized. User must be logged in.')
    }
    return this.dbManager
  }

  async createBlock(block: Block) {
    try {
      const data = {
        ...block,
        meta: block.meta ? JSON.stringify(block.meta) : '[]',
        annotations: block.annotations ? JSON.stringify(block.annotations) : '[]',
        contents: block.contents
      }
      const dbManager = await this.ensureDbManager()
      await dbManager.insert('Block', data)
    } catch (error) {
      console.error('Failed to create block:', error)
      throw error
    }
  }

  async getBlockByUuid(uuid: string) {
    try {
      const dbManager = await this.ensureDbManager()
      const results = await dbManager.select('Block', { uuid })
      return results[0] as Block | undefined
    } catch (error) {
      console.error('Failed to fetch block:', error)
      return undefined
    }
  }

  async getAllBlocks() {
    try {
      const dbManager = await this.ensureDbManager()
      const results = await dbManager.select('Block')
      console.log('results', results)
      return results as Block[]
    } catch (error) {
      console.error('Failed to fetch blocks:', error)
      return []
    }
  }

  async updateBlock(block: Partial<Block> & { uuid: string }) {
    try {
      const { uuid, ...updateData } = block
      if (updateData.meta) {
        updateData.meta = JSON.stringify(updateData.meta)
      }
      if (updateData.annotations) {
        updateData.annotations = JSON.stringify(updateData.annotations)
      }
      const dbManager = await this.ensureDbManager()
      await dbManager.update('Block', updateData, { uuid })
    } catch (error) {
      console.error('Failed to update block:', error)
      throw error
    }
  }

  async deleteBlock(uuid: string) {
    try {
      const dbManager = await this.ensureDbManager()
      await dbManager.delete('Block', { uuid })
    } catch (error) {
      console.error('Failed to delete block:', error)
      throw error
    }
  }
}

export function useBlockService() {
  const blockService = new BlockService()

  return {
    createBlock: (block: Block) => blockService.createBlock(block),
    getBlockByUuid: (uuid: string) => blockService.getBlockByUuid(uuid),
    getAllBlocks: () => blockService.getAllBlocks(),
    updateBlock: (block: Partial<Block> & { uuid: string }) => blockService.updateBlock(block),
    deleteBlock: (uuid: string) => blockService.deleteBlock(uuid)
  }
}
