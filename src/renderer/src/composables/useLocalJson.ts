import { ref, computed } from 'vue'
import { useUserStore } from '@renderer/stores/user'
import { FileSystemManager } from '@renderer/api/uniform/fs'

interface LocalJsonData {
  [key: string]: any
}

// 内存中的写入锁，防止并发写入
const writeLocks = new Map<string, Promise<void>>()

// 全局数据缓存，按 namespace 分组
const globalDataCache = new Map<string, LocalJsonData>()

// 导出全局缓存以供测试使用
export { globalDataCache }

export function useLocalJson(namespace: string) {
  const userStore = useUserStore()
  const fs = new FileSystemManager()

  // 响应式状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算文件路径
  const filePath = computed(() => {
    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in to use local JSON storage')
    }
    return `example/users/${userStore.userInfo.uuid}/kv/${namespace}.json`
  })

  // 获取缓存键
  const cacheKey = computed(() => {
    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in to use local JSON storage')
    }
    return `${userStore.userInfo.uuid}:${namespace}`
  })

  // 确保 KV 目录存在
  async function ensureKvDirectory() {
    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in')
    }

    const kvDir = `example/users/${userStore.userInfo.uuid}/kv`
    try {
      // 检查目录是否存在，如果不存在则创建
      await fs.stat(kvDir)
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(kvDir)
    }
  }

  // 初始化数据从 JSON 文件加载到内存
  async function load() {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      await ensureKvDirectory()

      // 使用 readFile 而不是 readJson，手动解析 JSON
      const fileContent = await fs.readFile(filePath.value)

      // 检查文件内容是否为空或只包含空白字符
      const trimmedContent = fileContent.trim()
      if (!trimmedContent) {
        // 文件为空，初始化为空对象
        globalDataCache.set(cacheKey.value, {})
        return
      }

      // 尝试解析 JSON
      let fileData
      try {
        fileData = JSON.parse(trimmedContent)
      } catch (parseError) {
        console.warn('Invalid JSON format, reinitializing file:', parseError)
        // JSON 格式错误，重新初始化文件
        globalDataCache.set(cacheKey.value, {})
        await syncToFile() // 重新写入正确的 JSON 格式
        return
      }

      // 缓存数据到内存
      globalDataCache.set(cacheKey.value, fileData || {})
    } catch (err) {
      // 如果文件不存在，初始化为空对象
      if (err instanceof Error && (
        err.message.includes('not found') ||
        err.message.includes('ENOENT') ||
        err.message.includes('no such file')
      )) {
        globalDataCache.set(cacheKey.value, {})
      } else {
        error.value = err instanceof Error ? err.message : 'Failed to load data'
        console.error('Failed to load local JSON:', err)
        // 即使加载失败，也初始化为空对象，确保系统可用
        globalDataCache.set(cacheKey.value, {})
      }
    } finally {
      isLoading.value = false
    }
  }

  // 同步保存到 JSON 文件（带并发保护）
  async function syncToFile() {
    const lockKey = filePath.value
    const data = globalDataCache.get(cacheKey.value) || {}

    // 如果已经有写入操作在进行，等待它完成
    if (writeLocks.has(lockKey)) {
      await writeLocks.get(lockKey)
    }

    // 创建新的写入锁
    const writePromise = performSave(data)
    writeLocks.set(lockKey, writePromise)

    try {
      await writePromise
    } finally {
      writeLocks.delete(lockKey)
    }
  }

  // 实际的保存操作
  async function performSave(data: LocalJsonData) {
    try {
      await ensureKvDirectory()

      // 深度序列化数据，确保所有复杂对象都能正确序列化
      const serializedData = JSON.parse(JSON.stringify(data))

      // 使用 writeFile 而不是 writeJson，手动序列化为字符串
      await fs.writeFile(filePath.value, JSON.stringify(serializedData, null, 2))
      error.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to save data'
      console.error('Failed to save local JSON:', err)
      throw err
    }
  }

  // 获取内存中的数据
  function getData(): LocalJsonData {
    return globalDataCache.get(cacheKey.value) || {}
  }

  // 设置内存中的数据
  function setData(newData: LocalJsonData) {
    globalDataCache.set(cacheKey.value, newData)
  }

  // 同步获取值
  function get<T = any>(key: string, defaultValue?: T): T {
    const data = getData()
    return data[key] !== undefined ? data[key] : (defaultValue as T)
  }

  // 同步设置值，并异步同步到文件
  function set(key: string, value: any) {
    const data = getData()
    const newData = { ...data, [key]: value }
    setData(newData)

    // 异步同步到文件，不阻塞当前操作
    syncToFile().catch(err => {
      console.error('Failed to sync to file:', err)
    })
  }

  // 同步删除值，并异步同步到文件
  function remove(key: string) {
    const data = getData()
    const newData = { ...data }
    delete newData[key]
    setData(newData)

    // 异步同步到文件，不阻塞当前操作
    syncToFile().catch(err => {
      console.error('Failed to sync to file:', err)
    })
  }

  // 同步清空所有数据，并异步同步到文件
  function clear() {
    setData({})

    // 异步同步到文件，不阻塞当前操作
    syncToFile().catch(err => {
      console.error('Failed to sync to file:', err)
    })
  }

  // 检查是否存在某个键
  function has(key: string): boolean {
    const data = getData()
    return key in data
  }

  // 获取所有键
  function keys(): string[] {
    const data = getData()
    return Object.keys(data)
  }

  // 获取所有值
  function values(): any[] {
    const data = getData()
    return Object.values(data)
  }

  // 获取所有键值对
  function entries(): [string, any][] {
    const data = getData()
    return Object.entries(data)
  }

  // 批量设置，并异步同步到文件
  function setMany(entries: Record<string, any>) {
    const data = getData()
    const newData = { ...data, ...entries }
    setData(newData)

    // 异步同步到文件，不阻塞当前操作
    syncToFile().catch(err => {
      console.error('Failed to sync to file:', err)
    })
  }

  // 批量删除，并异步同步到文件
  function removeMany(keys: string[]) {
    const data = getData()
    const newData = { ...data }
    keys.forEach(key => delete newData[key])
    setData(newData)

    // 异步同步到文件，不阻塞当前操作
    syncToFile().catch(err => {
      console.error('Failed to sync to file:', err)
    })
  }

  // 初始化时加载数据
  load()

  return {
    // 响应式状态
    data: computed(() => getData()),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),

    // 核心方法（同步）
    get,
    set,
    remove,
    clear,

    // 实用方法（同步）
    has,
    keys,
    values,
    entries,
    setMany,
    removeMany,

    // 手动操作
    load,
    syncToFile
  }
}
