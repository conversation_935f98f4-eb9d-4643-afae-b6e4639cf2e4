import { ref, computed, type ComputedRef } from 'vue'
import { useUserStore } from '@renderer/stores/user'
import { FileSystemManager } from '@renderer/api/uniform/fs'

/**
 * 本地JSON数据结构接口
 */
interface LocalJsonData {
  [key: string]: any
}

/**
 * 错误类型枚举
 */
enum LocalJsonErrorType {
  USER_NOT_LOGGED_IN = 'USER_NOT_LOGGED_IN',
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  FILE_WRITE_ERROR = 'FILE_WRITE_ERROR',
  JSON_PARSE_ERROR = 'JSON_PARSE_ERROR',
  DIRECTORY_CREATE_ERROR = 'DIRECTORY_CREATE_ERROR',
  CONCURRENT_WRITE_ERROR = 'CONCURRENT_WRITE_ERROR'
}

/**
 * 自定义错误类
 */
class LocalJsonError extends Error {
  constructor(
    public type: LocalJsonErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'LocalJsonError'
  }
}

/**
 * useLocalJson 返回值接口
 */
interface UseLocalJsonReturn {
  // 响应式状态
  data: ComputedRef<LocalJsonData>
  isLoading: ComputedRef<boolean>
  error: ComputedRef<string | null>

  // 核心方法（同步）
  get: <T = any>(key: string, defaultValue?: T) => T
  set: (key: string, value: any) => void
  remove: (key: string) => void
  clear: () => void

  // 实用方法（同步）
  has: (key: string) => boolean
  keys: () => string[]
  values: () => any[]
  entries: () => [string, any][]
  setMany: (entries: Record<string, any>) => void
  removeMany: (keys: string[]) => void

  // 手动操作
  load: () => Promise<void>
  syncToFile: () => Promise<void>
}

/**
 * 写入队列项接口
 */
interface WriteQueueItem {
  promise: Promise<void>
  timestamp: number
}

// 内存中的写入锁，防止并发写入 - 改进为队列机制
const writeLocks = new Map<string, WriteQueueItem>()

// 全局数据缓存，按 namespace 分组
const globalDataCache = new Map<string, LocalJsonData>()

// 防抖定时器缓存
const debounceTimers = new Map<string, NodeJS.Timeout>()

// 防抖延迟时间（毫秒）
const DEBOUNCE_DELAY = 300

// 写入锁超时时间（毫秒）
const WRITE_LOCK_TIMEOUT = 10000

// 导出全局缓存以供测试使用
export { globalDataCache, LocalJsonError, LocalJsonErrorType }

/**
 * 用户维度的本地JSON存储系统
 *
 * @param namespace - 命名空间，用于区分不同的存储文件
 * @returns UseLocalJsonReturn 包含所有存储操作方法的对象
 *
 * @example
 * ```typescript
 * // 存储到 example/users/:uuid/kv/example.json
 * const { set, get } = useLocalJson('example')
 * set('key1', 'value1')
 * const value = get('key1')
 *
 * // 存储到 example/users/:uuid/kv/aaa.json
 * const { set: setAaa, get: getAaa } = useLocalJson('aaa')
 * ```
 */
export function useLocalJson(namespace: string): UseLocalJsonReturn {
  // 参数验证
  if (!namespace || typeof namespace !== 'string') {
    throw new LocalJsonError(
      LocalJsonErrorType.USER_NOT_LOGGED_IN,
      'Namespace must be a non-empty string'
    )
  }

  const userStore = useUserStore()
  const fs = new FileSystemManager()

  // 响应式状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  /**
   * 获取当前用户UUID，带错误处理
   */
  const getUserUuid = (): string => {
    const uuid = userStore.userInfo?.uuid
    if (!uuid) {
      throw new LocalJsonError(
        LocalJsonErrorType.USER_NOT_LOGGED_IN,
        'User must be logged in to use local JSON storage'
      )
    }
    return uuid
  }

  // 计算文件路径
  const filePath = computed(() => {
    const uuid = getUserUuid()
    return `example/users/${uuid}/kv/${namespace}.json`
  })

  // 获取缓存键
  const cacheKey = computed(() => {
    const uuid = getUserUuid()
    return `${uuid}:${namespace}`
  })

  /**
   * 确保 KV 目录存在，支持递归创建
   */
  async function ensureKvDirectory(): Promise<void> {
    const uuid = getUserUuid()
    const kvDir = `example/users/${uuid}/kv`

    try {
      await fs.stat(kvDir)
    } catch (statError) {
      try {
        // 递归创建目录结构
        await fs.mkdir(kvDir)
      } catch (mkdirError) {
        throw new LocalJsonError(
          LocalJsonErrorType.DIRECTORY_CREATE_ERROR,
          `Failed to create KV directory: ${kvDir}`,
          mkdirError instanceof Error ? mkdirError : new Error(String(mkdirError))
        )
      }
    }
  }

  /**
   * 验证JSON数据的完整性
   */
  function validateJsonData(data: any): LocalJsonData {
    if (data === null || data === undefined) {
      return {}
    }

    if (typeof data !== 'object' || Array.isArray(data)) {
      console.warn('Invalid JSON data format, expected object, got:', typeof data)
      return {}
    }

    return data as LocalJsonData
  }

  /**
   * 安全的JSON解析，带错误恢复
   */
  function safeJsonParse(content: any): LocalJsonData {
    let stringContent: string

    // 确保 content 是字符串
    if (typeof content === 'string') {
      stringContent = content
    } else if (content instanceof Uint8Array) {
      // 处理 Uint8Array
      try {
        stringContent = new TextDecoder('utf-8').decode(content)
      } catch (decodeError) {
        console.warn('Failed to decode Uint8Array in safeJsonParse:', decodeError)
        return {}
      }
    } else if (content && typeof content === 'object' && 'buffer' in content) {
      // 处理 Node.js Buffer
      try {
        stringContent = content.toString('utf-8')
      } catch (convertError) {
        console.warn('Failed to convert Buffer in safeJsonParse:', convertError)
        return {}
      }
    } else {
      console.warn('Content is not a supported type, got:', typeof content, content)
      return {}
    }

    const trimmedContent = stringContent.trim()
    if (!trimmedContent) {
      return {}
    }

    try {
      const parsed = JSON.parse(trimmedContent)
      return validateJsonData(parsed)
    } catch (parseError) {
      console.warn('JSON parse error, content:', trimmedContent.substring(0, 100))
      throw new LocalJsonError(
        LocalJsonErrorType.JSON_PARSE_ERROR,
        'Failed to parse JSON content',
        parseError instanceof Error ? parseError : new Error(String(parseError))
      )
    }
  }

  /**
   * 安全的JSON序列化
   */
  function safeJsonStringify(data: LocalJsonData): string {
    try {
      // 深度克隆以避免循环引用
      const clonedData = JSON.parse(JSON.stringify(data))
      return JSON.stringify(clonedData, null, 2)
    } catch (stringifyError) {
      throw new LocalJsonError(
        LocalJsonErrorType.FILE_WRITE_ERROR,
        'Failed to serialize JSON data',
        stringifyError instanceof Error ? stringifyError : new Error(String(stringifyError))
      )
    }
  }

  /**
   * 改进的并发写入锁机制
   */
  async function acquireWriteLock(lockKey: string): Promise<void> {
    const existingLock = writeLocks.get(lockKey)

    if (existingLock) {
      // 检查锁是否超时
      const now = Date.now()
      if (now - existingLock.timestamp > WRITE_LOCK_TIMEOUT) {
        console.warn(`Write lock timeout for ${lockKey}, removing stale lock`)
        writeLocks.delete(lockKey)
      } else {
        // 等待现有锁完成
        try {
          await existingLock.promise
        } catch (error) {
          console.warn('Previous write operation failed:', error)
        }
      }
    }
  }

  /**
   * 防抖写入机制
   */
  function debouncedSyncToFile(): void {
    const lockKey = filePath.value

    // 清除之前的定时器
    const existingTimer = debounceTimers.get(lockKey)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的防抖定时器
    const timer = setTimeout(() => {
      syncToFile().catch(err => {
        console.error('Debounced sync to file failed:', err)
      })
      debounceTimers.delete(lockKey)
    }, DEBOUNCE_DELAY)

    debounceTimers.set(lockKey, timer)
  }

  /**
   * 初始化数据从 JSON 文件加载到内存
   * 改进的错误处理和数据验证
   */
  async function load(): Promise<void> {
    if (isLoading.value) {
      console.debug('Load already in progress, skipping')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      await ensureKvDirectory()

      let fileContent: any
      try {
        fileContent = await fs.readFile(filePath.value)
      } catch (readError) {
        // 文件不存在是正常情况，初始化为空对象
        if (readError instanceof Error && (
          readError.message.includes('not found') ||
          readError.message.includes('ENOENT') ||
          readError.message.includes('no such file')
        )) {
          globalDataCache.set(cacheKey.value, {})
          return
        }

        throw new LocalJsonError(
          LocalJsonErrorType.FILE_READ_ERROR,
          `Failed to read file: ${filePath.value}`,
          readError instanceof Error ? readError : new Error(String(readError))
        )
      }

      // 确保文件内容是字符串 - 处理各种可能的返回类型
      let stringContent: string
      if (typeof fileContent === 'string') {
        stringContent = fileContent
      } else if (fileContent instanceof Uint8Array) {
        // 处理 Uint8Array
        try {
          stringContent = new TextDecoder('utf-8').decode(fileContent)
          console.log('Successfully decoded Uint8Array to string')
        } catch (decodeError) {
          console.error('Failed to decode Uint8Array to string:', decodeError)
          globalDataCache.set(cacheKey.value, {})
          return
        }
      } else if (fileContent && typeof fileContent === 'object' && 'buffer' in fileContent) {
        // 处理 Node.js Buffer
        try {
          stringContent = fileContent.toString('utf-8')
          console.log('Successfully converted Buffer to string')
        } catch (convertError) {
          console.error('Failed to convert Buffer to string:', convertError)
          globalDataCache.set(cacheKey.value, {})
          return
        }
      } else {
        // 尝试转换为字符串
        try {
          stringContent = String(fileContent)
          console.log('Successfully converted to string using String()')
        } catch (convertError) {
          console.error('Failed to convert file content to string:', convertError)
          globalDataCache.set(cacheKey.value, {})
          return
        }
      }

      // 使用安全的JSON解析
      try {
        const fileData = safeJsonParse(stringContent)
        globalDataCache.set(cacheKey.value, fileData)
      } catch (parseError) {
        if (parseError instanceof LocalJsonError) {
          console.warn('JSON parse failed, reinitializing file:', parseError.message)
          // JSON 格式错误，重新初始化文件
          globalDataCache.set(cacheKey.value, {})
          await syncToFile() // 重新写入正确的 JSON 格式
        } else {
          throw parseError
        }
      }

    } catch (err) {
      const errorMessage = err instanceof LocalJsonError
        ? err.message
        : (err instanceof Error ? err.message : 'Unknown error occurred')

      error.value = errorMessage
      console.error('Failed to load local JSON:', err)

      // 即使加载失败，也初始化为空对象，确保系统可用
      globalDataCache.set(cacheKey.value, {})
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 同步保存到 JSON 文件（改进的并发保护）
   */
  async function syncToFile(): Promise<void> {
    const lockKey = filePath.value
    const data = globalDataCache.get(cacheKey.value) || {}

    // 使用改进的锁机制
    await acquireWriteLock(lockKey)

    // 创建新的写入锁
    const writePromise = performSave(data)
    const lockItem: WriteQueueItem = {
      promise: writePromise,
      timestamp: Date.now()
    }
    writeLocks.set(lockKey, lockItem)

    try {
      await writePromise
    } catch (saveError) {
      console.error('Sync to file failed:', saveError)
      throw saveError
    } finally {
      writeLocks.delete(lockKey)
    }
  }

  /**
   * 实际的保存操作，改进的错误处理
   */
  async function performSave(data: LocalJsonData): Promise<void> {
    try {
      await ensureKvDirectory()

      // 使用安全的JSON序列化
      const jsonContent = safeJsonStringify(data)

      // 写入文件
      await fs.writeFile(filePath.value, jsonContent)
      error.value = null

    } catch (err) {
      const saveError = new LocalJsonError(
        LocalJsonErrorType.FILE_WRITE_ERROR,
        `Failed to save file: ${filePath.value}`,
        err instanceof Error ? err : new Error(String(err))
      )

      error.value = saveError.message
      console.error('Failed to save local JSON:', saveError)
      throw saveError
    }
  }

  /**
   * 获取内存中的数据（优化性能，避免不必要的复制）
   */
  function getData(): LocalJsonData {
    const cached = globalDataCache.get(cacheKey.value)
    return cached || {}
  }

  /**
   * 设置内存中的数据（优化性能）
   */
  function setData(newData: LocalJsonData): void {
    globalDataCache.set(cacheKey.value, newData)
  }



  /**
   * 同步获取值，支持泛型和默认值
   * @param key - 要获取的键名
   * @param defaultValue - 键不存在时的默认值
   * @returns 键对应的值或默认值
   */
  function get<T = any>(key: string, defaultValue?: T): T {
    if (typeof key !== 'string') {
      console.warn('Key must be a string, got:', typeof key)
      return defaultValue as T
    }

    const data = getData()
    const value = data[key]
    return value !== undefined ? value : (defaultValue as T)
  }

  /**
   * 同步设置值，使用防抖机制异步同步到文件
   * @param key - 要设置的键名
   * @param value - 要设置的值（必须是JSON可序列化的）
   */
  function set(key: string, value: any): void {
    if (typeof key !== 'string') {
      console.warn('Key must be a string, got:', typeof key)
      return
    }

    try {
      // 验证值是否可序列化
      JSON.stringify(value)
    } catch (serializeError) {
      console.error('Value is not JSON serializable:', serializeError)
      return
    }

    const data = getData()
    const newData = { ...data, [key]: value }
    setData(newData)

    // 使用防抖机制异步同步到文件
    debouncedSyncToFile()
  }

  /**
   * 同步删除值，使用防抖机制异步同步到文件
   * @param key - 要删除的键名
   */
  function remove(key: string): void {
    if (typeof key !== 'string') {
      console.warn('Key must be a string, got:', typeof key)
      return
    }

    const data = getData()
    if (!(key in data)) {
      return // 键不存在，无需操作
    }

    const newData = { ...data }
    delete newData[key]
    setData(newData)

    // 使用防抖机制异步同步到文件
    debouncedSyncToFile()
  }

  /**
   * 同步清空所有数据，使用防抖机制异步同步到文件
   */
  function clear(): void {
    setData({})

    // 使用防抖机制异步同步到文件
    debouncedSyncToFile()
  }

  /**
   * 检查是否存在某个键
   * @param key - 要检查的键名
   * @returns 键是否存在
   */
  function has(key: string): boolean {
    if (typeof key !== 'string') {
      console.warn('Key must be a string, got:', typeof key)
      return false
    }

    const data = getData()
    return key in data
  }

  /**
   * 获取所有键
   * @returns 所有键的数组
   */
  function keys(): string[] {
    const data = getData()
    return Object.keys(data)
  }

  /**
   * 获取所有值
   * @returns 所有值的数组
   */
  function values(): any[] {
    const data = getData()
    return Object.values(data)
  }

  /**
   * 获取所有键值对
   * @returns 所有键值对的数组
   */
  function entries(): [string, any][] {
    const data = getData()
    return Object.entries(data)
  }

  /**
   * 批量设置多个键值对，使用防抖机制异步同步到文件
   * @param entries - 要设置的键值对对象
   */
  function setMany(entries: Record<string, any>): void {
    if (!entries || typeof entries !== 'object' || Array.isArray(entries)) {
      console.warn('Entries must be a plain object, got:', typeof entries)
      return
    }

    // 验证所有值都是可序列化的
    try {
      JSON.stringify(entries)
    } catch (serializeError) {
      console.error('Some values are not JSON serializable:', serializeError)
      return
    }

    const data = getData()
    const newData = { ...data, ...entries }
    setData(newData)

    // 使用防抖机制异步同步到文件
    debouncedSyncToFile()
  }

  /**
   * 批量删除多个键，使用防抖机制异步同步到文件
   * @param keys - 要删除的键名数组
   */
  function removeMany(keys: string[]): void {
    if (!Array.isArray(keys)) {
      console.warn('Keys must be an array, got:', typeof keys)
      return
    }

    if (keys.length === 0) {
      return // 空数组，无需操作
    }

    // 验证所有键都是字符串
    const validKeys = keys.filter(key => {
      if (typeof key !== 'string') {
        console.warn('All keys must be strings, skipping:', key)
        return false
      }
      return true
    })

    if (validKeys.length === 0) {
      return // 没有有效的键
    }

    const data = getData()
    const newData = { ...data }
    let hasChanges = false

    validKeys.forEach(key => {
      if (key in newData) {
        delete newData[key]
        hasChanges = true
      }
    })

    if (hasChanges) {
      setData(newData)
      // 使用防抖机制异步同步到文件
      debouncedSyncToFile()
    }
  }

  // 初始化时加载数据（异步，不阻塞函数返回）
  load().catch(err => {
    console.error('Initial load failed:', err)
  })

  // 返回完整的API接口
  return {
    // 响应式状态
    data: computed(() => getData()),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),

    // 核心方法（同步）
    get,
    set,
    remove,
    clear,

    // 实用方法（同步）
    has,
    keys,
    values,
    entries,
    setMany,
    removeMany,

    // 手动操作
    load,
    syncToFile
  } as UseLocalJsonReturn
}
