/**
 * useAIChat - AI聊天核心逻辑管理
 */

import { ref, nextTick } from 'vue'
import type { J<PERSON><PERSON>ontent } from '@tiptap/core'
import TurndownService from 'turndown'

import type {
  AIMessage,
  AIMode,
  AIChatOptions,
  MessageProcessResult,
  AIProcessingState
} from '@renderer/types/chronEngine'
import {
  COZE_CONFIG,
  TYPEWRITER_CONFIG,
  MESSAGE_TEMPLATES,
  TOOL_CALLING,
  DEFAULT_EDITOR_CONTENT
} from '@renderer/constants/chronEngine'
import { CozeUtils, type CozeMessage } from '@renderer/utils/CozeUtils'
import { useBlockService } from '@renderer/composables/useBlockService'
import { useUserStore } from '@renderer/stores/user'
import { showToast } from '@renderer/utils/toast'
import { useImageUpload } from '@renderer/composables/useImageUpload'
import {
  detectToolCalling,
  parseToolCall,
  processToolCallingInResponse
} from '@renderer/utils/toolCalling'
import { TypeWriter } from '@renderer/utils/typeWriter'

export function useAIChat() {
  // 状态管理
  const aiConversation = ref<AIMessage[]>([])
  const aiPrompt = ref('')
  const isAiProcessing = ref(false)
  const currentConversationId = ref('')
  const currentAiRequest = ref<(() => void) | null>(null)
  const selectedMode = ref<AIMode>('Smart')
  const selectedDeepSearch = ref(false)

  // 用户存储
  const userStore = useUserStore()
  const { cozeFileManager, getAllCozeFileIds } = useImageUpload()

  /**
   * 处理编辑器内容，提取文本、图片和提及
   */
  const processEditorContent = async (serializedContent: JSONContent | undefined): Promise<MessageProcessResult> => {
    if (!serializedContent) {
      return {
        displayContent: '',
        aiPrompt: '',
        files: []
      }
    }

    let promptText = ''
    let displayContent = ''
    const files: any[] = []

    if (serializedContent.content) {
      for (const node of serializedContent.content) {
        if (node.type === 'paragraph' && node.content) {
          for (const innerNode of node.content) {
            if (innerNode.type === 'text') {
              promptText += innerNode.text
              displayContent += innerNode.text
            } else if (innerNode.type === 'image' || innerNode.type === 'simpleImage') {
              const src = innerNode.attrs?.src as string
              if (src) {
                files.push({
                  type: 'image',
                  transfer_method: 'remote_url',
                  url: src
                })
              }
              promptText += '[图片]'
              displayContent += '[图片]'
            } else if (innerNode.type === 'mention') {
              const content = await useBlockService().getBlockByUuid(innerNode.attrs?.id)
              const turndownService = new TurndownService()
              const contents = turndownService.turndown(content?.contents || '')

              promptText += `用户提到了文章：${content?.title || '未知文章'},它的内容是：<quote>${contents}</quote>`
              displayContent += `@${content?.title || innerNode.attrs?.id}`
            }
          }
        }
      }
    }

    return {
      displayContent,
      aiPrompt: promptText,
      files
    }
  }

  /**
   * 创建TypeWriter实例
   */
  const createTypewriter = (
    messageIndex: number,
    enableThinking: boolean,
    onCompleted?: () => void
  ): TypeWriter => {
    return new TypeWriter(
      (char: string) => {
        if (aiConversation.value[messageIndex]) {
          if (!enableThinking) {
            aiConversation.value[messageIndex].content += char
          } else {
            if (aiConversation.value[messageIndex].thinkingCompleted) {
              aiConversation.value[messageIndex].content += char
            } else if (aiConversation.value[messageIndex].isThinking) {
              // 思考阶段的字符处理逻辑
            } else {
              aiConversation.value[messageIndex].content += char
            }
          }
        }
      },
      TYPEWRITER_CONFIG.DEFAULT_INTERVAL,
      onCompleted
    )
  }

  /**
   * 处理AI文本流的复杂逻辑
   */
  const processTextStream = (
    chunk: string,
    messageIndex: number,
    enableThinking: boolean,
    currentTypewriter: TypeWriter | null,
    streamState: {
      accumulatedRawStream: string
      lastProcessedStreamLength: number
      thinkingContentBuffer: string
      inThinkingBlock: boolean
    }
  ) => {
    streamState.accumulatedRawStream += chunk
    let streamToProcess = streamState.accumulatedRawStream.substring(streamState.lastProcessedStreamLength)

    const { THINKING_START_TAG, THINKING_END_TAG, STEP_START_TAG, STEP_END_TAG } = TOOL_CALLING

    let currentPos = 0
    let textToType = ''

    while (currentPos < streamToProcess.length) {
      if (!streamState.inThinkingBlock) {
        const thinkStartIdx = streamToProcess.indexOf(THINKING_START_TAG, currentPos)
        const toolCallMarkerIdx = detectToolCalling(streamToProcess.substring(currentPos))
        const actualToolCallMarkerIdx = toolCallMarkerIdx !== -1 ? currentPos + toolCallMarkerIdx : -1

        let nextSpecialIdx = -1
        if (thinkStartIdx !== -1 && actualToolCallMarkerIdx !== -1) {
          nextSpecialIdx = Math.min(thinkStartIdx, actualToolCallMarkerIdx)
        } else if (thinkStartIdx !== -1) {
          nextSpecialIdx = thinkStartIdx
        } else if (actualToolCallMarkerIdx !== -1) {
          nextSpecialIdx = actualToolCallMarkerIdx
        }

        if (nextSpecialIdx !== -1) {
          textToType += streamToProcess.substring(currentPos, nextSpecialIdx)
          currentPos = nextSpecialIdx
        } else {
          textToType += streamToProcess.substring(currentPos)
          currentPos = streamToProcess.length
        }

        if (textToType) {
          if (!enableThinking) {
            currentTypewriter?.enqueue(textToType)
          } else {
            if (
              aiConversation.value[messageIndex] &&
              aiConversation.value[messageIndex].thinkingCompleted
            ) {
              currentTypewriter?.enqueue(textToType)
            } else if (
              aiConversation.value[messageIndex] &&
              !aiConversation.value[messageIndex].isThinking
            ) {
              currentTypewriter?.enqueue(textToType)
            }
          }
          textToType = ''
        }

        // 处理思考标签开始
        if (currentPos < streamToProcess.length) {
          if (streamToProcess.startsWith(THINKING_START_TAG, currentPos)) {
            streamState.inThinkingBlock = true
            aiConversation.value[messageIndex].isThinking = true
            aiConversation.value[messageIndex].thinkingCompleted = false
            streamState.thinkingContentBuffer = THINKING_START_TAG
            currentPos += THINKING_START_TAG.length
          }
        }
      } else {
        // 在思考块内部
        const thinkEndIdx = streamToProcess.indexOf(THINKING_END_TAG, currentPos)
        if (thinkEndIdx !== -1) {
          streamState.thinkingContentBuffer += streamToProcess.substring(
            currentPos,
            thinkEndIdx + THINKING_END_TAG.length
          )
          aiConversation.value[messageIndex].thinkingContent = streamState.thinkingContentBuffer
          aiConversation.value[messageIndex].isThinking = false
          aiConversation.value[messageIndex].thinkingCompleted = true
          streamState.thinkingContentBuffer = ''

          streamState.inThinkingBlock = false
          currentPos = thinkEndIdx + THINKING_END_TAG.length

          const remainingAfterThinking = streamToProcess.substring(currentPos)
          if (remainingAfterThinking) {
            currentTypewriter?.enqueue(remainingAfterThinking)
          }
          currentPos = streamToProcess.length
        } else {
          streamState.thinkingContentBuffer += streamToProcess.substring(currentPos)
          const steps: string[] = []
          let stepMatch
          const stepRegex = new RegExp(`${STEP_START_TAG}(.*?)${STEP_END_TAG}`, 'gs')
          let tempThinkingContent = THINKING_START_TAG
          while ((stepMatch = stepRegex.exec(streamState.thinkingContentBuffer)) !== null) {
            steps.push(stepMatch[1].trim())
            tempThinkingContent += `${STEP_START_TAG}${stepMatch[1].trim()}${STEP_END_TAG}`
          }
          aiConversation.value[messageIndex].thinkingContent = tempThinkingContent
          currentPos = streamToProcess.length
        }
      }
    }

    streamState.lastProcessedStreamLength = streamState.accumulatedRawStream.length
  }

  /**
   * 处理AI请求的核心逻辑
   */
  const processAiRequest = async (promptText: string, files: any[]) => {
    const enableThinking = selectedMode.value === 'Smart'

    const tempResponseIndex = aiConversation.value.push({
      role: 'assistant',
      content: '',
      formattedContent: '',
      isThinking: enableThinking,
      thinkingContent: '',
      thinkingCompleted: false,
      isProcessingToolCall: false,
      toolCode: undefined
    }) - 1

    let currentTypewriter: TypeWriter | null = null
    const resetAndCreateTypewriter = (onCompleted?: () => void): TypeWriter => {
      if (currentTypewriter) {
        currentTypewriter.clear()
      }
      currentTypewriter = createTypewriter(tempResponseIndex, enableThinking, onCompleted)
      return currentTypewriter
    }

    resetAndCreateTypewriter(() => {
      nextTick(() => {
        // 处理完成后的回调
      })
    })

    try {
      aiConversation.value[tempResponseIndex].content = ''

      const historyMessages = aiConversation.value.slice(0, -1).map((msg) => ({
        role: msg.role,
        content: msg.content
      })) as CozeMessage[]

      if (!currentConversationId.value) {
        try {
          const conversation = await CozeUtils.createConversation({
            botId: COZE_CONFIG.BOT_ID,
            metaData: { source: 'ChronEngine' }
          })
          currentConversationId.value = conversation.id
        } catch (error) {
          console.error('创建会话失败:', error)
        }
      }

      const cozeFileIds = getAllCozeFileIds()

      let accumulatedRawStream = ''
      let lastProcessedStreamLength = 0
      let thinkingContentBuffer = ''
      let inThinkingBlock = false

      const streamState = {
        accumulatedRawStream,
        lastProcessedStreamLength,
        thinkingContentBuffer,
        inThinkingBlock
      }

      console.log('cozeFileIds', cozeFileIds)

      currentAiRequest.value = await CozeUtils.chat(promptText, {
        messages: historyMessages.slice(0, -1),
        conversationId: currentConversationId.value,
        parameters: {
          TOKEN: userStore.token,
          IMAGE: cozeFileIds.length > 0 ? cozeFileIds.map((id) => JSON.stringify({ file_id: id })) : undefined,
          HISTORY_MESSAGES: historyMessages,
          AUTO: selectedMode.value === 'Smart',
          WEBSEARCH: selectedMode.value === 'Simple' && selectedDeepSearch.value
        },
        onStart: () => {
          console.log('开始处理请求')
          aiConversation.value[tempResponseIndex].isThinking = enableThinking
          aiConversation.value[tempResponseIndex].thinkingCompleted = false
          aiConversation.value[tempResponseIndex].content = ''
          aiConversation.value[tempResponseIndex].thinkingContent = ''

          // 重置流状态
          Object.assign(streamState, {
            accumulatedRawStream: '',
            lastProcessedStreamLength: 0,
            thinkingContentBuffer: '',
            inThinkingBlock: false
          })
        },
        onText: async (chunk) => {
          processTextStream(chunk, tempResponseIndex, enableThinking, currentTypewriter, streamState)
          nextTick(() => {
            // 滚动处理
          })
        },
        onError: (error) => {
          console.error('Coze API错误:', error)
          const errorMessage = MESSAGE_TEMPLATES.ERROR_GENERIC.replace('{error}', error)
          if (currentTypewriter && currentTypewriter.isBusy) {
            currentTypewriter.clear()
          }
          const newErrorTypewriter = resetAndCreateTypewriter(() => {
            isAiProcessing.value = false
          })
          newErrorTypewriter?.enqueue(errorMessage)

          if (aiConversation.value[tempResponseIndex]) {
            aiConversation.value[tempResponseIndex].isThinking = false
            aiConversation.value[tempResponseIndex].thinkingCompleted = false
          }
        },
        onCompleted: () => {
          console.log('对话完成')
          const completeProcessing = () => {
            if (!aiConversation.value[tempResponseIndex]) {
              isAiProcessing.value = false
              return
            }

            if (
              !aiConversation.value[tempResponseIndex]?.content?.trim() &&
              !aiConversation.value[tempResponseIndex]?.thinkingContent?.trim() &&
              !aiConversation.value[tempResponseIndex]?.toolCode
            ) {
              if (currentTypewriter && currentTypewriter.isBusy) {
                currentTypewriter.clear()
              }
              const newEmptyResponseTypewriter = resetAndCreateTypewriter(() => {
                isAiProcessing.value = false
              })
              newEmptyResponseTypewriter?.enqueue(MESSAGE_TEMPLATES.ERROR_EMPTY_RESPONSE)
            }

            if (currentTypewriter && currentTypewriter.isBusy) {
              currentTypewriter.onAllCharsTyped = () => {
                isAiProcessing.value = false
              }
            } else {
              isAiProcessing.value = false
            }

            if (aiConversation.value[tempResponseIndex]) {
              if (
                !aiConversation.value[tempResponseIndex].thinkingCompleted &&
                aiConversation.value[tempResponseIndex].thinkingContent
              ) {
                aiConversation.value[tempResponseIndex].thinkingCompleted = true
              }
              aiConversation.value[tempResponseIndex].isThinking = false
            }
          }

          if (currentTypewriter && currentTypewriter.isBusy) {
            currentTypewriter.onAllCharsTyped = completeProcessing
          } else {
            completeProcessing()
          }
        },
        onConversationId: (id) => {
          currentConversationId.value = id
        }
      })
    } catch (error) {
      console.error('发送消息错误:', error)
      const errorMessage = MESSAGE_TEMPLATES.ERROR_NETWORK
      if (currentTypewriter && currentTypewriter.isBusy) {
        currentTypewriter.clear()
      }
      const newCatchErrorTypewriter = resetAndCreateTypewriter(() => {
        isAiProcessing.value = false
      })
      newCatchErrorTypewriter?.enqueue(errorMessage)

      if (aiConversation.value[tempResponseIndex]) {
        aiConversation.value[tempResponseIndex].isThinking = false
        aiConversation.value[tempResponseIndex].thinkingCompleted = false
      }

      isAiProcessing.value = false
    }
  }

  /**
   * 发送AI消息
   */
  const sendAiMessage = async (editorContent: JSONContent) => {
    const { displayContent, aiPrompt: promptText, files } = await processEditorContent(editorContent)

    if (promptText.trim().length === 0 && files.length === 0) return
    if (isAiProcessing.value) return

    // 添加用户消息
    aiConversation.value.push({
      role: 'user',
      content: promptText,
      formattedContent: displayContent
    })

    isAiProcessing.value = true
    await processAiRequest(promptText, files)
  }

  /**
   * 发送外部消息
   */
  const sendExternalMessage = async (message: string) => {
    if (!message || message.trim().length === 0 || isAiProcessing.value) {
      return
    }

    aiConversation.value.push({
      role: 'user',
      content: message.trim(),
      formattedContent: message.trim()
    })

    isAiProcessing.value = true
    await processAiRequest(message.trim(), [])
  }

  /**
   * 清空对话
   */
  const clearConversation = async () => {
    aiConversation.value = []
    currentConversationId.value = ''

    try {
      const conversation = await CozeUtils.createConversation({
        botId: COZE_CONFIG.BOT_ID,
        metaData: { source: 'ChronEngine' }
      })
      currentConversationId.value = conversation.id
    } catch (error) {
      console.error('创建新会话失败:', error)
    }
  }

  /**
   * 取消当前请求
   */
  const cancelCurrentRequest = () => {
    if (currentAiRequest.value) {
      currentAiRequest.value()
      currentAiRequest.value = null
    }
    isAiProcessing.value = false
  }

  /**
   * 重置AI聊天状态
   */
  const resetAIChat = () => {
    cancelCurrentRequest()
    aiConversation.value = []
    currentConversationId.value = ''
    aiPrompt.value = ''
    isAiProcessing.value = false
  }

  return {
    // 状态
    aiConversation,
    aiPrompt,
    isAiProcessing,
    currentConversationId,
    selectedMode,
    selectedDeepSearch,

    // 方法
    processEditorContent,
    sendAiMessage,
    sendExternalMessage,
    clearConversation,
    cancelCurrentRequest,
    resetAIChat
  }
}
