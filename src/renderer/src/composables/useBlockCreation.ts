import { BlockJsonNode } from '@renderer/componentSystem/BlockJsonNode'
import { mitter } from '@renderer/plugins/mitter'
import { setCurrentResource } from '@renderer/utils/bus'
import { resourceType } from '@renderer/componentSystem/types/resourceTypes'
import { useDockviewStore } from '@renderer/stores/dockviewStore'

export function useBlockCreation() {
  const createBlock = async (
    resourceType: resourceType = 'note',
    parentId: string | null = null
  ) => {
    const newNode = await BlockJsonNode.createRootBlock(resourceType)
    if (newNode) {
      // 发送事件通知创建了新区块，以便滚动到新创建的区块
      mitter.emit('note-created', {
        noteId: newNode.uuid,
        parentId,
        shouldScroll: true
      })

      // 如果是笔记类型、PDF类型或星流类型，则自动打开
      if (resourceType === 'note' || resourceType === 'pdf' || resourceType === 'xingliu') {
        setCurrentResource(newNode.uuid, resourceType)

        // 使用 dockview 系统打开新笔记
        const dockviewStore = useDockviewStore()
        
        const panel = dockviewStore.addPanel({
          id: `${resourceType}-${newNode.uuid}`,
          title: newNode.title || `新建${resourceType === 'note' ? '笔记' : resourceType === 'pdf' ? 'PDF' : '星流'}`,
          component: 'panel',
          params: {
            type: resourceType,
            node: newNode
          }
        })
        
        if (panel) {
          dockviewStore.setActivePanelId(panel.id)
        }
      }
    }
    return newNode
  }

  return {
    createBlock
  }
}
