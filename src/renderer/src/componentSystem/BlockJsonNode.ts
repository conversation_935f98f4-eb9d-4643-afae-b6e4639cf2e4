import { FileSystemManager } from '@renderer/api/uniform/fs'
import { useBlockService } from '@renderer/composables/useBlockService'
import { UpdateService } from '@renderer/editor/provider/UpdateService'
import { useNodeStore } from '@renderer/stores/nodeStore'
import { useUserStore } from '@renderer/stores/user'
import { useConfig } from '@renderer/utils/configHelper'
import { showToast } from '@renderer/utils/toast'
import { v4 } from 'uuid'
import { BaseNode } from './common/BaseNode'
import { addPDF } from './function/pdf'
import { type resourceType } from './types/resourceTypes'

import { getPlatform } from '@renderer/utils/platform'
import moment from 'moment'
import path from 'path-browserify-esm'

// 节点基础接口
export interface BlockNode {
  uuid: string
  type: resourceType
  title: string
  createdAt: number
  updatedAt: number
  meta: string[]
  children: BlockNode[]
  parent: string | null
  selection?: number
  contents?: string
}

/**
 * 区块节点类 - 用于管理笔记和PDF资源
 */
export class BlockJsonNode extends BaseNode {
  constructor(jsonNode: BlockNode) {
    super({
      uuid: jsonNode.uuid,
      type: jsonNode.type,
      title: jsonNode.title,
      createdAt: jsonNode.createdAt,
      updatedAt: jsonNode.updatedAt,
      meta: jsonNode.meta,
      parent: jsonNode.parent,
      children: jsonNode.children
    })

    // 递归处理子节点
    if (jsonNode.children && Array.isArray(jsonNode.children)) {
      this.children = jsonNode.children.map((childJson) => {
        const childNode = new BlockJsonNode(childJson)
        childNode.parent = this
        return childNode
      })
    }
  }

  getType(): resourceType {
    return this.type
  }

  /**
   * 创建根节点
   * @param resourceType 节点类型 ('pdf' | 'note' | 'xingliu')
   * @returns 创建的节点实例，如果取消则返回 undefined
   */
  static async createRootBlock(resourceType: resourceType) {
    const uuid = v4()
    const blockService = useBlockService()
    const nodeStore = useNodeStore()
    let name: string | undefined
    const now = moment().valueOf()

    // 数据库支持 'pdf'、'note' 和 'xingliu'
    // 直接使用原始类型，不再进行转换
    const dbType = resourceType

    // 创建数据库记录
    if (resourceType === 'pdf') {
      name = await BlockJsonNode.handlePDFCreation(uuid)
      if (!name) return undefined // 用户取消了PDF选择

      blockService.createBlock({
        uuid,
        type: dbType,
        title: name,
        createdAt: now,
        updatedAt: now,
        meta: JSON.stringify([])
      })
    } else if (resourceType === 'xingliu') {
      blockService.createBlock({
        uuid,
        type: dbType,
        title: '新星流节点',
        createdAt: now,
        updatedAt: now,
        contents: '',
        meta: JSON.stringify([])
      })
    } else {
      blockService.createBlock({
        uuid,
        type: dbType,
        title: '新笔记',
        createdAt: now,
        updatedAt: now,
        meta: JSON.stringify([])
      })
    }

    // 创建内存节点
    let nodeTitle = '新笔记'
    if (resourceType === 'pdf') {
      nodeTitle = name ?? '新PDF'
    } else if (resourceType === 'xingliu') {
      nodeTitle = '新星流节点'
    }

    const newNode = new BlockJsonNode({
      uuid,
      type: resourceType,
      title: nodeTitle,
      createdAt: now,
      updatedAt: now,
      meta: [],
      parent: null,
      children: []
    })

    nodeStore.memoryNodes.push(newNode)

    // 保存到磁盘
    await BlockJsonNode.saveToDisk()

    // 返回创建的节点
    return newNode
  }

  /**
   * 处理PDF文件的创建
   * @param uuid 节点ID
   * @returns PDF文件名（不含扩展名）
   */
  private static async handlePDFCreation(uuid: string): Promise<string | undefined> {
    const result = await addPDF()
    if (!result) {
      showToast('错误', '取消选择了PDF文件', 1000)
      return undefined
    }

    const config = useConfig()
    const userStore = useUserStore()
    
    if (!userStore.userInfo?.uuid) {
      showToast('错误', '用户必须登录才能创建PDF', 2000)
      return undefined
    }
    
    const fs = new FileSystemManager()
    const platform = getPlatform()
    const userPdfPath = await config.getUserPdfPath(userStore.userInfo.uuid)
    const targetPath = path.resolve(userPdfPath, `${uuid}.pdf`)

    // 区分File对象和文件路径
    const isFileObject = result instanceof File
    const sourcePath = isFileObject ? (result as File).name : (result as string)

    // 提取文件名（无论是从路径还是从File对象）
    let fileName = sourcePath

    // 处理反斜杠和正斜杠（针对路径字符串）
    if (typeof fileName === 'string' && (fileName.includes('\\') || fileName.includes('/'))) {
      // 分割路径，取最后一部分
      const parts = fileName.split(/[\\\/]/)
      fileName = parts[parts.length - 1]
    }

    // 移除扩展名
    if (fileName.toLowerCase().endsWith('.pdf')) {
      fileName = fileName.substring(0, fileName.length - 4)
    }

    // 处理文件内容
    try {
      // Web环境特殊处理：从File对象读取
      if (platform === 'web' && isFileObject) {
        //TODO: 需要处理pdf文件的提取
        // // 在Web环境中，直接从File对象读取
        // pdfBuffer = await fs.readFileObjectAsBuffer(result as File)
        // // Web环境下可能需要将文件保存到本地存储中
        // // 这里使用localStorage作为示例，实际可能需要使用IndexedDB等
        // try {
        //   // 将二进制数据转换为Base64以便存储
        //   const base64 = BlockJsonNode.arrayBufferToBase64(pdfBuffer.buffer)
        //   localStorage.setItem(`pdf_${uuid}`, base64)
        //   console.log(`在Web环境中存储PDF(${fileName})到本地存储`)
        // } catch (storageError) {
        //   console.warn('无法在Web环境存储PDF:', storageError)
        // }
      }
      // Electron或其他环境：从文件路径读取
      else {
        // 先复制PDF文件到目标路径
        await fs.copyFile(sourcePath as string, targetPath)
        // 读取PDF文件内容
        // console.log(`尝试读取PDF文件: ${targetPath}`)
        // pdfBuffer = await fs.readFileAsBuffer(targetPath)
      }

      // if (!pdfBuffer || pdfBuffer.length === 0) {
      //   throw new Error('读取的PDF文件为空')
      // }

      // console.log(`成功读取PDF文件，大小: ${pdfBuffer.length} 字节，启动Worker处理...`)

      // 创建Web Worker处理PDF解析
      // BlockJsonNode.processWithWorker(pdfBuffer, uuid, config.pdfPath)
    } catch (error) {
      console.log(error)
      // 即使读取PDF文件失败，仍然返回文件名，不影响PDF节点创建
    }

    return fileName
  }

  /**
   * 保存节点变更到数据库
   */
  async saveChanges(): Promise<void> {
    const blockService = useBlockService()
    // 数据库现在支持 'pdf'、'note' 和 'xingliu'
    const dbType = this.type

    blockService.updateBlock({
      uuid: this.uuid,
      title: this.title,
      type: dbType,
      updatedAt: this.updatedAt,
      meta: JSON.stringify(this.meta)
    })

    await BlockJsonNode.saveToDisk()
  }

  /**
   * 添加根节点
   * @param title 节点标题
   * @param type 节点类型
   * @returns 新创建的节点
   */
  static async addRootNode(title?: string, type: resourceType = 'note'): Promise<BlockJsonNode> {
    const uuid = v4()
    const store = useNodeStore()
    const blockService = useBlockService()
    const now = moment().valueOf()

    // 根据类型设置默认标题
    let nodeTitle = title || '新笔记'
    if (type === 'pdf') {
      nodeTitle = title || '新PDF'
    } else if (type === 'xingliu') {
      nodeTitle = title || '新星流节点'
    }

    // 数据库支持 'pdf'、'note' 和 'xingliu'
    // 直接使用原始类型，不再进行转换
    const dbType = type

    // 创建数据库记录
    blockService.createBlock({
      uuid,
      type: dbType,
      title: nodeTitle,
      createdAt: now,
      updatedAt: now,
      contents: type === 'note' || type === 'xingliu' ? '' : undefined,
      meta: JSON.stringify([])
    })

    // 创建内存节点
    const newJsonNode = new BlockJsonNode({
      uuid,
      type,
      title: nodeTitle,
      createdAt: now,
      updatedAt: now,
      meta: [],
      parent: null,
      children: []
    })

    store.memoryNodes.push(newJsonNode)
    await BlockJsonNode.saveToDisk()
    return newJsonNode
  }

  /**
   * 添加子节点
   * @param title 节点标题
   * @param type 节点类型
   * @returns 新创建的节点
   */
  async addNode(title?: string, type: resourceType = 'note'): Promise<BaseNode> {
    const uuid = v4()
    const blockService = useBlockService()
    const now = moment().valueOf()
    let name: string | undefined

    // 数据库支持 'pdf'、'note' 和 'xingliu'
    // 直接使用原始类型，不再进行转换
    const dbType = type

    // 创建数据库记录
    if (type === 'pdf') {
      name = await BlockJsonNode.handlePDFCreation(uuid)
      if (!name) return this // 用户取消了PDF选择

      await blockService.createBlock({
        uuid,
        type: dbType,
        title: name,
        createdAt: now,
        updatedAt: now,
        meta: JSON.stringify([])
      })
    } else if (type === 'xingliu') {
      await blockService.createBlock({
        uuid,
        type: dbType,
        title: title ?? '新星流节点',
        createdAt: now,
        updatedAt: now,
        contents: '',
        meta: JSON.stringify([])
      })
    } else {
      await blockService.createBlock({
        uuid,
        type: dbType,
        title: title ?? '新笔记',
        createdAt: now,
        updatedAt: now,
        contents: '',
        meta: JSON.stringify([])
      })
    }

    // 创建内存节点
    let nodeTitle = title ?? '新笔记'
    if (type === 'pdf') {
      nodeTitle = name ?? '新PDF'
    } else if (type === 'xingliu') {
      nodeTitle = title ?? '新星流节点'
    }

    const newJsonNode = new BlockJsonNode({
      uuid,
      type,
      title: nodeTitle,
      createdAt: now,
      updatedAt: now,
      meta: [],
      parent: this.uuid,
      children: []
    })

    // ------- 关键修复：确保将子节点添加到 nodeStore 中的真实父节点 -------
    const store = useNodeStore()
    // 尝试在内存树中找到真正的父节点
    const realParent = store.getNodeById(this.uuid) as BlockJsonNode | null

    const targetParent = realParent || this

    // 设置父子关系
    newJsonNode.parent = targetParent
    targetParent.children.push(newJsonNode)

    // 如果是在克隆节点上操作，需要保证可见树刷新
    if (realParent) {
      // 触发一次文件树更新，让 visibleNodes 重新计算
      store.triggerTreeUpdate()
    }

    await BlockJsonNode.saveToDisk()
    return newJsonNode
  }

  /**
   * 添加已存在的节点作为子节点
   * @param nodeUuid 已存在节点的UUID
   * @returns 添加的节点
   */
  async addExistingNode(nodeUuid: string): Promise<BaseNode | null> {
    const nodeStore = useNodeStore()
    const existingNode = nodeStore.getNodeById(nodeUuid)

    // 如果节点不存在，返回null
    if (!existingNode) {
      console.error(`Node with UUID ${nodeUuid} not found`)
      return null
    }

    // 如果节点已经有父节点，先从父节点中移除
    if (existingNode.parent) {
      existingNode.parent.children = existingNode.parent.children.filter(
        (child) => child.uuid !== nodeUuid
      )
    } else {
      // 如果是顶级节点，从根节点列表中移除
      nodeStore.memoryNodes = nodeStore.memoryNodes.filter((node) => node.uuid !== nodeUuid)
    }

    // 添加到当前节点的子节点列表
    existingNode.parent = this
    this.children.push(existingNode)

    // 保存变更
    await BlockJsonNode.saveToDisk()

    return existingNode
  }

  /**
   * 保存所有节点到磁盘
   */
  static async saveToDisk() {
    const store = useNodeStore()
    const userStore = useUserStore()
    if (!userStore.userInfo?.uuid) {
      throw new Error('用户必须登录才能保存区块')
    }

    // 构建可序列化的节点树 - 收集所有根节点及其子节点
    const serializedNodes: BlockNode[] = []
    store.memoryNodes.forEach((node) => {
      const treeNodes = BlockJsonNode.buildSerializableTree(node as BlockJsonNode)
      serializedNodes.push(...treeNodes)
    })

    // 写入文件 - 使用新的用户专属路径
    const manager = new FileSystemManager()
    const config = useConfig()
    const userWorkspace = await config.getUserWorkspace(userStore.userInfo.uuid)
    const blocksPath = path.resolve(userWorkspace, 'blocks.json')
    await manager.writeJson(blocksPath, JSON.stringify(serializedNodes))
  }

  /**
   * 构建可序列化的节点树
   * @param node 节点
   * @param parentId 父节点ID
   * @returns 可序列化的节点数组
   */
  private static buildSerializableTree(node: BlockJsonNode, parentId?: string): BlockNode[] {
    const newNode: BlockNode = {
      uuid: node.uuid,
      type: node.type,
      title: node.title,
      createdAt: node.createdAt,
      updatedAt: node.updatedAt,
      meta: node.meta,
      children: [],
      parent: parentId ? parentId : null
    }

    if (node.children && node.children.length > 0) {
      // 获取所有子节点的平面数组
      const childNodes: BlockNode[] = node.children.map((child) => {
        // 为每个子节点创建一个新的序列化节点
        const childNode: BlockNode = {
          uuid: child.uuid,
          type: child.type,
          title: child.title,
          createdAt: child.createdAt,
          updatedAt: child.updatedAt,
          meta: child.meta,
          children: [],
          parent: node.uuid
        }

        // 递归处理子节点的子节点
        if (child.children && child.children.length > 0) {
          childNode.children = BlockJsonNode.getChildrenRecursively(child as BlockJsonNode)
        }

        return childNode
      })

      newNode.children = childNodes
    }

    return [newNode]
  }

  /**
   * 递归获取子节点
   * @param node 父节点
   * @returns 所有子节点的数组
   */
  private static getChildrenRecursively(node: BlockJsonNode): BlockNode[] {
    if (!node.children || node.children.length === 0) {
      return []
    }

    return node.children.map((child) => {
      const childNode: BlockNode = {
        uuid: child.uuid,
        type: child.type,
        title: child.title,
        createdAt: child.createdAt,
        updatedAt: child.updatedAt,
        meta: child.meta,
        children: [],
        parent: node.uuid
      }

      if (child.children && child.children.length > 0) {
        childNode.children = BlockJsonNode.getChildrenRecursively(child as BlockJsonNode)
      }

      return childNode
    })
  }

  /**
   * 删除节点及其所有子节点
   */
  async removeNode() {
    const store = useNodeStore()
    const blockService = useBlockService()
    const config = useConfig()
    const userStore = useUserStore()
    const fs = new FileSystemManager()

    // 如果是PDF节点，删除对应的PDF文件
    if (this.type === 'pdf') {
      if (userStore.userInfo?.uuid) {
        const userPdfPath = config.getUserPdfPath(userStore.userInfo.uuid)
        const pdfPath = path.resolve(userPdfPath, `${this.uuid}.pdf`)
        try {
          await fs.remove(pdfPath)
        } catch (error) {
          console.error('Failed to delete PDF file:', error)
        }
      }
    } else if (this.type === 'note') {
      // 如果是Note节点，删除对应的更新记录
      const updateService = new UpdateService()
      try {
        await updateService.clearUpdates(this.uuid)
      } catch (error) {
        console.error('Failed to delete note updates:', error)
      }
    } else if (this.type === 'todo') {
      // 如果是Todo节点（待办清单），先删除清单中包含的所有todos
      console.log(`Deleting todo list node: ${this.uuid}`)
      try {
        // 动态导入TodoService避免循环依赖
        const { useTodoService } = await import('@renderer/services/TodoService')
        const todoService = useTodoService()

        // 获取待办清单信息
        const todoList = await todoService.getTodoList(this.uuid)
        if (todoList && todoList.todoIds && todoList.todoIds.length > 0) {
          console.log(
            `Todo list ${this.uuid} contains ${todoList.todoIds.length} todos, deleting them recursively`
          )

          // 递归删除清单中的所有todos
          for (const todoId of todoList.todoIds) {
            await todoService.deleteTodoRecursively(todoId)
          }

          console.log(`Successfully deleted all todos in list ${this.uuid}`)
        } else {
          console.log(`Todo list ${this.uuid} is empty or not found`)
        }
      } catch (error) {
        console.error('Failed to delete todos in todo list:', error)
      }
    }

    // 递归删除所有子节点
    await this.removeChildrenRecursively(this)

    // 删除数据库记录
    await blockService.deleteBlock(this.uuid)

    // 从父节点或根节点列表中移除
    if (this.parent?.children) {
      this.parent.children = this.parent.children.filter((child) => child !== this)
    } else {
      const index = store.memoryNodes.indexOf(this)
      if (index !== -1) {
        store.memoryNodes.splice(index, 1)
      }
    }

    await BlockJsonNode.saveToDisk()
  }

  /**
   * 递归删除子节点
   * @param node 要删除子节点的节点
   */
  async removeChildrenRecursively(node: BlockJsonNode) {
    if (node.children && node.children.length > 0) {
      // 使用for...of来按顺序处理每个子节点
      for (const child of node.children) {
        await (child as BlockJsonNode).removeNode()
      }
    }
  }
}
