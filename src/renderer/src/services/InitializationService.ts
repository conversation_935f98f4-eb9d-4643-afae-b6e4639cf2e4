import { useNodeStore } from '@renderer/stores/nodeStore'
import { useSettingsStore } from '@renderer/stores/settings'
import { UserInfo, useUserStore } from '@renderer/stores/user'
// import { performDatabaseMigration } from '@renderer/utils/dbMigration'
import { useTodoService } from './TodoService'
import { useConfig } from '@renderer/utils/configHelper'
import { useMigrationService } from '@renderer/utils/migrationService'
import { useStorageCompatibilityAdapter } from '@renderer/utils/storageCompatibilityAdapter'

function isUserInfo(value: unknown): value is UserInfo {
  return (
    !!value &&
    typeof value === 'object' &&
    'uuid' in value &&
    typeof (value as any).uuid === 'string'
  )
}

export class InitializationService {
  /**
   * 初始化设置
   */
  static async initSettings() {
    const settingsStore = useSettingsStore()
    settingsStore.initSettings()
  }

  /**
   * 初始化数据库相关的 repositories
   */
  static async initRepositories() {
    const userStore = useUserStore()
    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in to initialize repositories')
    }

    // 确保用户目录结构存在
    await this.ensureUserDirectories()

    // 执行数据库迁移
    // await performDatabaseMigration()
  }

  /**
   * 确保用户目录结构存在（包含自动迁移）
   */
  static async ensureUserDirectories() {
    const userStore = useUserStore()
    const config = useConfig()

    if (!userStore.userInfo?.uuid) {
      throw new Error('User must be logged in to ensure directories')
    }

    const userId = userStore.userInfo.uuid

    try {
      // 1. 优先检查迁移状态，避免重复迁移
      const adapter = useStorageCompatibilityAdapter()

      // 检查用户级迁移状态文件
      try {
        const userMigrationStatus = await adapter.getUserMigrationStatus(userId)
        if (userMigrationStatus && userMigrationStatus.migrated && userMigrationStatus.version === 'v2.0') {
          console.log(`用户 ${userId} 迁移状态显示已完成迁移，跳过迁移检测`)
          // 确保目录完整性
          const validation = await adapter.validateStorageIntegrity(userId)
          if (!validation.isValid) {
            console.log(`目录不完整，创建缺失目录...`)
            await config.createUserDirectories(userId)
            console.log(`用户目录结构创建完成`)
          } else {
            console.log(`目录结构完整`)
          }
          return
        }
      } catch (error) {
        console.log('用户迁移状态文件不存在或读取失败，继续存储模式检测')
      }

      // 2. 检测当前存储模式
      const currentMode = await adapter.detectStorageMode(userId)

      console.log(`检测到用户 ${userId} 的存储模式: ${currentMode}`)

      if (currentMode === 'legacy') {
        // 3. 发现老架构，跳转到迁移界面
        console.log('检测到老架构数据，跳转到迁移界面...')

        // 导入router并跳转到迁移页面
        const router = (await import('@renderer/router/router')).default
        await router.push('/migration')
        return
      } else {
        // 4. 新架构，确保目录完整性
        const validation = await adapter.validateStorageIntegrity(userId)
        if (!validation.isValid) {
          console.log(`新架构目录不完整，创建缺失目录...`)
          await config.createUserDirectories(userId)
          console.log(`用户目录结构创建完成`)
        } else {
          console.log(`新架构目录结构已存在且完整`)
        }
      }
    } catch (error) {
      console.error('确保用户目录结构失败:', error)
      throw error
    }
  }

  /**
   * 清理老架构文件
   * @param users 用户列表
   */
  static async cleanupLegacyFiles(users: string[]) {
    try {
      const config = useConfig()
      const fs = new (await import('@renderer/api/uniform/fs')).FileSystemManager()

      console.log(`开始清理 ${users.length} 个用户的老架构文件...`)

      for (const userId of users) {
        try {
          // 删除老架构数据库文件
          const legacyDbPath = `${config.workspacePath}/chron_${userId}.db`
          try {
            await fs.remove(legacyDbPath)
            console.log(`✅ 已删除老架构数据库: ${legacyDbPath}`)
          } catch (error) {
            console.warn(`无法删除老架构数据库: ${legacyDbPath}`, error)
          }

          // 删除老架构blocks文件
          const legacyBlocksPath = `${config.workspacePath}/blocks_${userId}.json`
          try {
            await fs.remove(legacyBlocksPath)
            console.log(`✅ 已删除老架构blocks文件: ${legacyBlocksPath}`)
          } catch (error) {
            console.warn(`无法删除老架构blocks文件: ${legacyBlocksPath}`, error)
          }

        } catch (error) {
          console.error(`清理用户 ${userId} 的老架构文件失败:`, error)
        }
      }

      console.log('✅ 老架构文件清理完成')

    } catch (error) {
      console.error('清理老架构文件过程失败:', error)
      // 清理失败不应该影响应用运行，所以不抛出错误
    }
  }

  /**
   * 执行自动迁移
   */
  static async performAutoMigration(userId: string) {
    try {
      const migrationService = useMigrationService()
      const adapter = useStorageCompatibilityAdapter()

      // 1. 扫描现有结构
      console.log('扫描现有存储结构...')
      const scanResult = await migrationService.scanExistingStructure()

      if (!scanResult.needsMigration) {
        console.log('无需迁移')
        return
      }

      if (scanResult.users.length === 0) {
        console.log('未发现用户数据，创建新架构目录')
        const config = useConfig()
        await config.createUserDirectories(userId)
        return
      }

      console.log(`发现 ${scanResult.users.length} 个用户的数据需要迁移`)

      // 2. 创建迁移前备份
      console.log('创建迁移前备份...')
      const backupPath = await migrationService.createPreMigrationBackup()
      console.log(`备份创建完成: ${backupPath}`)

      // 3. 执行迁移
      console.log('执行数据迁移...')
      await migrationService.performConservativeMigration(scanResult.users)

      // 4. 验证迁移结果
      console.log('验证迁移结果...')
      const verificationResult = await migrationService.verifyMigration(scanResult.users)

      if (verificationResult) {
        console.log('✅ 自动迁移完成！所有数据已成功迁移到新架构')

        // 5. 为每个用户更新迁移状态
        for (const user of scanResult.users) {
          await adapter.saveUserMigrationStatus(user, {
            userId: user,
            version: 'v2.0',
            migrated: true,
            rollbackAvailable: true,
            backupPath: backupPath
          })
        }

        // 6. 清理老架构文件（可选，延迟清理更安全）
        // 注意：为了安全起见，我们暂时不自动删除老架构文件
        // 用户可以在确认新架构工作正常后手动删除，或者我们可以在后续版本中添加定时清理
        // console.log('清理老架构文件...')
        // await this.cleanupLegacyFiles(scanResult.users)
        console.log('💡 提示：老架构文件已保留以便回滚，不会影响新架构的正常使用')
        console.log('💡 由于检测优先级为 modern > legacy，系统将继续使用新架构')

        // 7. 清除适配器缓存，确保重新检测
        adapter.clearCache()

      } else {
        console.error('❌ 迁移验证失败')
        const report = migrationService.getReport()
        if (report.errors.length > 0) {
          console.error('迁移错误:', report.errors)
        }
        throw new Error('自动迁移失败，请检查日志')
      }

    } catch (error) {
      console.error('自动迁移过程失败:', error)

      // 迁移失败时，仍然可以使用老架构运行
      console.log('迁移失败，应用将继续使用老架构运行')
      // 不抛出错误，允许应用继续使用兼容模式
    }
  }

  /**
   * 初始化笔记数据
   */
  static async initNotes() {
    const nodeStore = useNodeStore()
    await nodeStore.initNotes()
  }

  /**
   * 初始化待办事项服务
   */
  static async initTodos() {
    try {
      // 初始化待办事项服务（这会创建todos表）
      useTodoService()
      console.log('Todos service initialized')
    } catch (error) {
      console.error('Failed to initialize todos service:', error)
      // 不抛出错误，允许应用继续运行
    }
  }

  /**
   * 初始化所有用户相关的数据
   * 在用户登录后或应用启动时（如果用户已登录）调用
   */
  static async initUserData() {
    const userStore = useUserStore()
    if (!userStore.userInfo?.uuid) {
      return
    }

    try {
      await this.initRepositories()
      await this.initNotes()
      await this.initTodos()
    } catch (error) {
      console.error('Failed to initialize user data:', error)
      throw error
    }
  }

  /**
   * 重新初始化用户数据
   * 用于迁移完成后或数据需要重新加载时
   */
  static async reinitUserData() {
    const userStore = useUserStore()
    if (!userStore.userInfo?.uuid) {
      return
    }

    try {
      console.log('重新初始化用户数据...')

      // 清除 nodeStore 缓存
      const nodeStore = useNodeStore()
      nodeStore.clearData()

      // 重新初始化所有数据
      await this.initRepositories()
      await this.initNotes()
      await this.initTodos()

      console.log('用户数据重新初始化完成')
    } catch (error) {
      console.error('Failed to reinitialize user data:', error)
      throw error
    }
  }

  /**
   * 初始化垃圾回收站
   */
  static async initTrashStore() {
    try {
      const { useTrashStore } = await import('@renderer/stores/trashStore')
      const trashStore = useTrashStore()

      // 确保垃圾回收站已初始化
      await trashStore.initialize()

      console.log('Trash store initialized successfully')
    } catch (error) {
      console.error('Failed to initialize trash store:', error)
      // 不抛出错误，因为垃圾回收站初始化失败不应该阻止应用启动
    }
  }

  /**
   * 初始化应用
   * 在应用启动时调用
   */
  static async initializeApp() {
    try {
      // 1. 初始化设置（包括主题等所有配置）
      await this.initSettings()

      // 2. 如果用户已登录，初始化用户数据
      const userStore = useUserStore()
      const userInfo = userStore.userInfo

      if (userInfo && isUserInfo(userInfo)) {
        await this.initUserData()

        // 3. 初始化垃圾回收站（仅在用户已登录时）
        await this.initTrashStore()
      }
    } catch (error) {
      console.error('Failed to initialize app:', error)
      throw error
    }
  }
}
