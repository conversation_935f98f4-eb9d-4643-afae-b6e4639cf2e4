import { getPlatform, Platform } from '../../utils/platform'
import * as iOSFS from '../ios/fs'
import * as webFS from '../web/fs'

export class FileSystemManager {
  private platform: Platform

  constructor() {
    this.platform = getPlatform()
  }

  /**
   * 写入文件
   */
  async writeFile(path: string, data: string): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.writeFile(path, data)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'ios') {
      await iOSFS.writeFile(path, data)
    } else if (this.platform === 'web') {
      await webFS.writeFile(path, data)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 写入 JSON 文件
   */
  async writeJson(file: string, data: any): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.writeJson(file, data)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'ios') {
      await iOSFS.writeJson(file, data)
    } else if (this.platform === 'web') {
      await webFS.writeJson(file, data)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 读取 JSON 文件
   */
  async readJson<T = any>(file: string): Promise<T> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.readJson(file)
      if (!result?.success) throw new Error(result?.error)
      return result.data as T
    } else if (this.platform === 'ios') {
      return await iOSFS.readJson<T>(file)
    } else if (this.platform === 'web') {
      return await webFS.readJson<T>(file)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 删除文件
   */
  async remove(path: string): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.remove(path)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'ios') {
      await iOSFS.remove(path)
    } else if (this.platform === 'web') {
      await webFS.remove(path)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 创建目录
   */
  async mkdir(path: string): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.mkdirs(path)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'ios') {
      await iOSFS.mkdir(path)
    } else if (this.platform === 'web') {
      await webFS.mkdir(path)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 获取文件信息
   */
  async stat(path: string): Promise<any> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.stat(path)
      if (!result?.success) throw new Error(result?.error)
      return result.stats
    } else if (this.platform === 'ios') {
      return await iOSFS.stat(path)
    } else if (this.platform === 'web') {
      return await webFS.stat(path)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }
  /**
   * 读取文件内容（文本）
   */
  async readFile(path: string): Promise<string> {
    let content: any

    if (this.platform === 'electron') {
      content = await window.electronFS.readFileSync(path)
    } else if (this.platform === 'web') {
      content = await webFS.readFileSync(path)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }

    // 确保返回字符串
    if (typeof content === 'string') {
      return content
    } else if (content instanceof Uint8Array) {
      // 处理 Uint8Array
      return new TextDecoder('utf-8').decode(content)
    } else if (content && typeof content === 'object' && 'buffer' in content) {
      // 处理 Node.js Buffer
      return content.toString('utf-8')
    } else {
      // 尝试转换为字符串
      return String(content)
    }
  }

  async readFileSync(path: string): Promise<string> {
    return await this.readFile(path)
  }
  async copyFile(src: string, dest: string): Promise<void> {
    if (this.platform === 'electron') {
      await window.electronFS.copyFile(src, dest)
    } else if (this.platform === 'web') {
      await webFS.copyFile(src, dest)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 递归复制目录或文件
   * 用于复制整个目录树
   */
  async copy(src: string, dest: string): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.copy(src, dest)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'web') {
      await webFS.copy(src, dest)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 读取目录内容
   */
  async readdir(path: string): Promise<string[]> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.readdir(path)
      if (!result?.success) throw new Error(result?.error)
      return result.files
    } else if (this.platform === 'ios') {
      throw new Error(`Platform ${this.platform} is not supported`)
      // return await iOSFS.re(path)
    } else if (this.platform === 'web') {
      return await webFS.readdir(path)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  async writeBinaryFile(filePath: string, data: ArrayBuffer): Promise<void> {
    if (this.platform === 'electron') {
      const result = await window.electronFS.writeBinaryFile(filePath, data)
      if (!result?.success) throw new Error(result?.error)
    } else if (this.platform === 'ios') {
      await iOSFS.writeBinaryFile(filePath, data)
    } else if (this.platform === 'web') {
      await webFS.writeBinaryFile(filePath, data)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 读取二进制文件数据
   */
  async readFileAsBuffer(filePath: string): Promise<Uint8Array> {
    if (this.platform === 'electron') {
      try {
        // 使用新实现的readBinaryFile方法
        const result = await window.electronFS.readBinaryFile(filePath)
        if (result?.success && result.data) {
          return new Uint8Array(result.data)
        }

        // 如果读取失败，抛出错误
        if (!result?.success) {
          throw new Error(result?.error || '读取二进制文件失败')
        }

        // 如果到这里仍未返回，尝试备选方案
        console.warn('readBinaryFile返回了空数据，尝试备选方法')

        // 备选方案：使用readFileSync
        console.warn('使用readFileSync读取二进制文件，可能会损坏数据')
        const fileContent = await window.electronFS.readFileSync(filePath)
        const encoder = new TextEncoder()
        return encoder.encode(fileContent)
      } catch (error: any) {
        console.error('读取二进制文件失败:', error)
        throw new Error(`读取二进制文件失败: ${error.message}`)
      }
    } else if (this.platform === 'web') {
      // Web环境实现 - 使用我们的web文件系统
      try {
        const arrayBuffer = await webFS.readBinaryFile(filePath)
        return new Uint8Array(arrayBuffer)
      } catch (error: any) {
        console.error('Web环境下读取文件失败:', error)
        throw new Error(`Web环境下读取文件失败: ${error.message}`)
      }
    } else if (this.platform === 'ios') {
      // 需要在iOS文件系统中实现对应的方法
      throw new Error(`Platform ${this.platform} is not yet supported for binary file reading`)
    } else {
      throw new Error(`Platform ${this.platform} is not supported`)
    }
  }

  /**
   * 从文件对象读取二进制数据（专用于Web环境）
   * 这个方法可以处理用户通过文件上传框选择的文件
   * @param file 文件对象（通常来自input[type=file]或拖放事件）
   * @returns 文件的二进制数据
   */
  async readFileObjectAsBuffer(file: File): Promise<Uint8Array> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(new Uint8Array(reader.result))
        } else {
          reject(new Error('读取的数据不是二进制格式'))
        }
      }

      reader.onerror = () => {
        reject(new Error('读取文件失败: ' + reader.error?.message))
      }

      reader.readAsArrayBuffer(file)
    })
  }
}
