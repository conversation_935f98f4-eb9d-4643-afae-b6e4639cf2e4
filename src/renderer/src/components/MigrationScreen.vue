<template>
  <!-- 电影荧幕风格迁移界面 -->
  <div class="cinema-screen">
    <!-- 迁移进行中 -->
    <div v-if="migrationState === 'migrating'" class="fade-in">
      <div class="text breathing">正在迁移数据...</div>
    </div>
    
    <!-- 迁移完成 -->
    <div v-else-if="migrationState === 'completed'" class="fade-in">
      <div class="text">迁移完成</div>
      <div class="warning-notice fade-in-delayed">
        <div class="warning-title">⚠️ 重要提醒</div>
        <div class="warning-content">
          迁移后的备份功能已重新设计，<strong>旧版本的备份将不再适用</strong>。<br>
          如需查看之前的备份，请退回到迁移前的应用版本。
        </div>
      </div>
      <div class="button-group fade-in-delayed">
        <button @click="enterApp" class="enter-button">
          进入应用
        </button>
        <button @click="exitApp" class="exit-button">
          退出软件
        </button>
      </div>
    </div>
    
    <!-- 迁移错误 -->
    <div v-else-if="migrationState === 'error'" class="fade-in">
      <div class="text">迁移遇到问题</div>
      <div class="button-group fade-in-delayed">
        <button @click="retryMigration" class="retry-button">重试</button>
        <button @click="skipMigration" class="skip-button">跳过</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMigrationService } from '@renderer/utils/migrationService'
import { useUserStore } from '@renderer/stores/user'

// 迁移状态
const migrationState = ref<'migrating' | 'completed' | 'error'>('migrating')
const router = useRouter()
const userStore = useUserStore()

// 进入应用
const enterApp = async () => {
  try {
    // 迁移完成后，重新初始化用户数据
    const { InitializationService } = await import('@renderer/services/InitializationService')
    await InitializationService.reinitUserData()
    console.log('用户数据重新初始化完成')
  } catch (error) {
    console.error('重新初始化用户数据失败:', error)
  }
  
  router.push('/')
}

// 退出软件
const exitApp = () => {
  // 检查是否在 Electron 环境
  if (window.electron) {
    window.electron.closeApp()
  } else {
    // Web 环境下关闭当前标签页
    window.close()
  }
}

// 重试迁移
const retryMigration = () => {
  migrationState.value = 'migrating'
  performMigration()
}

// 跳过迁移
const skipMigration = async () => {
  try {
    // 跳过迁移时，仍然尝试初始化用户数据（以防有部分数据可以加载）
    const { InitializationService } = await import('@renderer/services/InitializationService')
    await InitializationService.initUserData()
    console.log('跳过迁移，用户数据初始化完成')
  } catch (error) {
    console.error('跳过迁移时初始化用户数据失败:', error)
  }
  
  // 跳过迁移，直接进入应用
  router.push('/')
}

// 执行迁移
const performMigration = async () => {
  try {
    const migrationService = useMigrationService()
    const userId = userStore.userInfo?.uuid
    
    if (!userId) {
      migrationState.value = 'error'
      return
    }

    // 执行迁移
    const report = await migrationService.performFullMigration()
    
    if (report.status === 'completed') {
      // 迁移成功，保存用户级迁移状态
      const { useStorageCompatibilityAdapter } = await import('@renderer/utils/storageCompatibilityAdapter')
      const adapter = useStorageCompatibilityAdapter()
      
      // 保存当前用户的迁移状态
      await adapter.saveUserMigrationStatus(userId, {
        userId,
        version: 'v2.0',
        migrated: true,
        rollbackAvailable: false
      })
      
      // 清除缓存
      adapter.clearCache()
      
      // 等待一下让状态文件写入完成
      await new Promise(resolve => setTimeout(resolve, 500))
      
      migrationState.value = 'completed'
    } else {
      migrationState.value = 'error'
    }
  } catch (error) {
    console.error('迁移失败:', error)
    migrationState.value = 'error'
  }
}

// 组件挂载时开始迁移
onMounted(() => {
  performMigration()
})
</script>

<style scoped>
.cinema-screen {
  background: #000000;
  color: #ffffff;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.text {
  font-size: 28px;
  font-weight: 300;
  letter-spacing: 2px;
  text-align: center;
  margin-bottom: 60px;
}

.enter-button {
  background: transparent;
  border: 1px solid #ffffff;
  color: #ffffff;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 300;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  min-width: 120px;
}

.enter-button:hover {
  background: #ffffff;
  color: #000000;
}

.exit-button {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 300;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  min-width: 120px;
}

.exit-button:hover {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.6);
}

.button-group {
  display: flex;
  gap: 24px;
  justify-content: center;
  align-items: center;
}

.retry-button,
.skip-button {
  background: transparent;
  border: 1px solid #ffffff;
  color: #ffffff;
  padding: 10px 24px;
  font-size: 14px;
  font-weight: 300;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
}

.retry-button:hover {
  background: #ffffff;
  color: #000000;
}

.skip-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 警告提醒样式 - 电影荧幕风格 */
.warning-notice {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0;
  padding: 24px;
  margin: 40px 0;
  max-width: 500px;
  text-align: center;
  position: relative;
}

.warning-notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.02);
  pointer-events: none;
}

.warning-title {
  font-size: 16px;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16px;
  text-align: center;
  letter-spacing: 1px;
}

.warning-content {
  font-size: 13px;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-weight: 300;
  letter-spacing: 0.5px;
}

.warning-content strong {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 2s ease-in-out;
}

.fade-in-delayed {
  animation: fadeIn 2s ease-in-out 1s both;
}

.breathing {
  animation: breathing 3s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes breathing {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text {
    font-size: 24px;
    margin-bottom: 40px;
  }
  
  .enter-button,
  .exit-button {
    padding: 10px 24px;
    font-size: 14px;
    min-width: 140px;
  }
  
  .button-group {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
  
  .warning-notice {
    margin: 20px 16px;
    padding: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .warning-title {
    font-size: 15px;
  }
  
  .warning-content {
    font-size: 13px;
  }
}
</style>