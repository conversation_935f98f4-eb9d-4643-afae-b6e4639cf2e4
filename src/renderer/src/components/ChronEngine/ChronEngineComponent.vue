<template>
  <div class="flex w-full flex-col h-[calc(100%-1.5rem)] relative chron-engine-container">
    <!-- 顶部工具栏 - 可通过hideTabs控制显示 -->
    <ChronEngineTabs
      v-if="!hideTabs"
      :panel-id="panel_id"
      :pane-node="node"
      :conversation="aiConversation"
      @close-pane="handleClosePane"
      @split-horizontal="handleSplitHorizontal"
      @split-vertical="handleSplitVertical"
      @clear-conversation="clearConversation"
    />

    <!-- 主内容区域 - 包裹层，用于定位背景 -->
    <div class="flex-1 relative h-full">
      <!-- 背景网格 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.03] pointer-events-none"></div>

      <!-- 主内容区域 -->
      <div class="absolute inset-0 overflow-hidden flex flex-col chron-engine-content">
        <!-- 内容区域 -->
        <div class="flex-1 flex flex-col relative overflow-auto z-10">
          <!-- 空状态显示 -->
          <ChronEngineEmptyView
            v-if="!aiConversation.length"
            :hide-tabs="hideTabs"
            class="flex-1 relative"
          />

          <!-- 对话显示区域 -->
          <ChronEngineConversation
            v-else
            :conversation="aiConversation"
            :is-processing="isAiProcessing"
            :hide-tabs="hideTabs"
            :menu-items="aiChatMenuItems"
            ref="conversationRef"
            @copy-message="copyMessage"
            @copy-ai-message="copyAiMessage"
            @delete-message="deleteAiMessage"
            @save-to-note="saveAiMessageToNote"
            @share-message="shareAiMessage"
            @clear-conversation="clearConversation"
          />

          <!-- 底部输入框 -->
          <ChronEngineInput
            v-model:editor-content="aiEditorContent"
            :is-processing="isAiProcessing"
            :hide-input="hideInput"
            :hide-tabs="hideTabs"
            :selected-mode="selectedMode"
            :selected-deep-search="selectedDeepSearch"
            :paste-handler="ChronEnginePasteHandler"
            ref="inputRef"
            @send-message="sendAiMessage"
            @toggle-deep-search="selectedDeepSearch = !selectedDeepSearch"
            @update-mode="selectedMode = $event"
            @keydown="handleKeyDown"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import type { JSONContent } from '@tiptap/core'

// 导入类型
import type {
  ChronEngineProps,
  ChronEngineEmits,
  ChronEngineExpose,
  MenuItem
} from '@renderer/types/chronEngine'

// 导入常量
import { DEFAULT_EDITOR_CONTENT, EVENTS } from '@renderer/constants/chronEngine'

// 导入工具函数
import { ChronEnginePasteHandler } from '@renderer/editor/plugins/function/pasteHandler'
import { mitter } from '@renderer/plugins/mitter'
import { registerAllToolHandlers } from '@renderer/utils/toolCalling/registerTools'

// 导入 stores
import { useDockviewStore } from '@renderer/stores/dockviewStore'
import { usePanelClose } from '@renderer/composables/usePanelClose'

// 导入composables
import { useAIChat } from '@renderer/composables/useAIChat'
import { useImageUpload } from '@renderer/composables/useImageUpload'
import { useScrollManager } from '@renderer/composables/useScrollManager'
import { useMessageActions } from '@renderer/composables/useMessageActions'

// 导入子组件
import ChronEngineTabs from './ChronEngineTabs.vue'
import ChronEngineEmptyView from './ChronEngineEmptyView.vue'
import ChronEngineConversation from './components/ChronEngineConversation.vue'
import ChronEngineInput from './components/ChronEngineInput.vue'

// 导入图标
import { Copy, Trash, Share } from 'lucide-vue-next'

// 定义Props和Emits
const props = defineProps<ChronEngineProps>()
const emit = defineEmits<ChronEngineEmits>()

// 计算属性
const hideInput = computed(() => props.hideInput || false)
const hideTabs = computed(() => props.hideTabs || false)

// 编辑器内容状态
const aiEditorContent = ref<JSONContent>(JSON.parse(JSON.stringify(DEFAULT_EDITOR_CONTENT)))

// 组件引用
const conversationRef = ref()
const inputRef = ref()

// 使用composables
const {
  aiConversation,
  isAiProcessing,
  selectedMode,
  selectedDeepSearch,
  sendAiMessage: sendAiMessageComposable,
  sendExternalMessage,
  clearConversation: clearConversationComposable,
  cancelCurrentRequest,
  resetAIChat
} = useAIChat()

const { cozeFileManager, processImageUrl, resetImageUpload } = useImageUpload()

const { setContainer, addScrollListener, removeScrollListener } = useScrollManager()

const { copyMessage, copyAiMessage, deleteAiMessage, saveAiMessageToNote, shareAiMessage } =
  useMessageActions(aiConversation)

// 使用面板关闭 composable
const { handleClosePanel } = usePanelClose()

// 上下文菜单项
const aiChatMenuItems = ref<MenuItem[]>([
  {
    id: 'copy',
    label: '复制消息',
    icon: Copy,
    action: () => {}
  },
  {
    id: 'delete',
    label: '删除消息',
    icon: Trash,
    action: () => {}
  },
  {
    id: 'share',
    label: '分享消息',
    icon: Share,
    action: () => {}
  }
])

/**
 * 处理键盘事件
 */
const handleKeyDown = (_e: KeyboardEvent) => {
  // 可以在这里添加其他键盘事件的处理逻辑
}

/**
 * 发送AI消息
 */
const sendAiMessage = async () => {
  await sendAiMessageComposable(aiEditorContent.value)

  // 清空编辑器
  aiEditorContent.value = JSON.parse(JSON.stringify(DEFAULT_EDITOR_CONTENT))

  // 聚焦输入框
  nextTick(() => {
    inputRef.value?.focusEditor()
  })
}

/**
 * 清空对话
 */
const clearConversation = async () => {
  await clearConversationComposable()
  resetImageUpload()
}

/**
 * 处理水平分屏事件
 */
const handleSplitHorizontal = () => {
  const dockviewStore = useDockviewStore()
  const api = dockviewStore.getApi()

  if (api && props.panel_id) {
    const currentPanel = api.getPanel(props.panel_id)
    if (currentPanel) {
      dockviewStore.addPanel({
        id: `${props.panel_id}-split-h-${Date.now()}`,
        title: 'Chron Engine',
        component: 'panel',
        params: {
          type: 'chronengine',
          node: props.node
        },
        position: {
          direction: 'right',
          referencePanel: props.panel_id
        }
      })
    }
  }
}

/**
 * 处理垂直分屏事件
 */
const handleSplitVertical = () => {
  const dockviewStore = useDockviewStore()
  const api = dockviewStore.getApi()

  if (api && props.panel_id) {
    const currentPanel = api.getPanel(props.panel_id)
    if (currentPanel) {
      dockviewStore.addPanel({
        id: `${props.panel_id}-split-v-${Date.now()}`,
        title: 'Chron Engine',
        component: 'panel',
        params: {
          type: 'chronengine',
          node: props.node
        },
        position: {
          direction: 'below',
          referencePanel: props.panel_id
        }
      })
    }
  }
}

/**
 * 处理关闭面板事件
 */
const handleClosePane = () => {
  handleClosePanel(props.node.uuid, props.panel_id)
}

/**
 * 添加图片到输入框
 */
const addImageToEditor = async (imageUrl: string, cozeFileId?: string) => {
  if (inputRef.value?.editor) {
    try {
      const processedUrl = await processImageUrl(imageUrl)

      // 将图片添加到编辑器中
      inputRef.value.insertContent({
        type: 'simpleImage',
        attrs: {
          src: processedUrl,
          width: '400px'
        }
      })

      // 如果有Coze文件ID，存储映射关系
      if (cozeFileId) {
        cozeFileManager.set(processedUrl, cozeFileId)
      }

      // 聚焦编辑器
      nextTick(() => {
        inputRef.value?.focusEditor()
      })
    } catch (error) {
      console.error('处理图片失败:', error)
    }
  }
}

/**
 * 添加文本到输入框
 */
const addTextToEditor = (text: string) => {
  if (inputRef.value) {
    // 清空编辑器内容
    inputRef.value.clearEditor()

    // 将文本添加到编辑器中
    inputRef.value.insertContent(text)

    // 聚焦编辑器
    nextTick(() => {
      inputRef.value?.focusEditor()
    })
  }
}

// 事件处理器
const handleCozeImageUploaded = (event: CustomEvent) => {
  const { imageUrl, cozeFileId } = event.detail
  if (imageUrl && cozeFileId) {
    cozeFileManager.set(imageUrl, cozeFileId)
    console.log('ChronEngine收到Coze图片上传事件:', { imageUrl, cozeFileId })
  }
}

const handleAddImage = (data: { panelId: string; imageUrl: string; cozeFileId?: string }) => {
  console.log(`[ChronEngine] 面板 ${props.panel_id} 收到 chronengine:add-image 事件`)
  if (data.panelId === props.panel_id) {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 匹配成功，添加图片到编辑器`)
    addImageToEditor(data.imageUrl, data.cozeFileId)
  }
}

const handleAddText = (data: { panelId: string; text: string }) => {
  console.log(`[ChronEngine] 面板 ${props.panel_id} 收到 chronengine:add-text 事件`)
  if (data.panelId === props.panel_id) {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 匹配成功，添加文本到编辑器`)
    addTextToEditor(data.text)
  }
}

// 监听对话变化自动滚动
watch(
  () => aiConversation.value.length,
  () => {
    nextTick(() => {
      conversationRef.value?.scrollToBottom()
    })
  }
)

// 监听最后一条消息内容变化
watch(
  () => {
    if (aiConversation.value.length > 0) {
      return aiConversation.value[aiConversation.value.length - 1].content
    }
    return ''
  },
  () => {
    nextTick(() => {
      conversationRef.value?.scrollToBottom()
    })
  }
)

// 暴露方法给父组件
const exposed: ChronEngineExpose = {
  sendExternalMessage
}

defineExpose(exposed)

onMounted(() => {
  // 注册所有工具处理器
  try {
    registerAllToolHandlers()
    console.log('工具处理器已注册')
  } catch (error) {
    console.error('注册工具处理器失败:', error)
  }

  // 注册自定义事件监听器
  document.addEventListener('coze-image-uploaded', handleCozeImageUploaded as EventListener)

  // 监听事件
  console.log(`[ChronEngine] 面板 ${props.panel_id} 注册事件监听器`)
  mitter.on(EVENTS.CHRONENGINE_ADD_IMAGE, handleAddImage)
  mitter.on(EVENTS.CHRONENGINE_ADD_TEXT, handleAddText)

  // 设置滚动容器
  if (conversationRef.value?.container) {
    setContainer(conversationRef.value.container)
    addScrollListener()
  }

  // 聚焦输入框
  nextTick(() => {
    if (!hideInput.value) {
      inputRef.value?.focusEditor()
    }
  })
})

onBeforeUnmount(() => {
  // 取消当前请求
  cancelCurrentRequest()

  // 重置状态
  resetAIChat()
  resetImageUpload()

  // 移除滚动事件监听
  removeScrollListener()

  // 移除事件监听
  console.log(`[ChronEngine] 面板 ${props.panel_id} 移除事件监听器`)
  mitter.off(EVENTS.CHRONENGINE_ADD_IMAGE, handleAddImage)
  mitter.off(EVENTS.CHRONENGINE_ADD_TEXT, handleAddText)

  // 移除自定义事件监听器
  document.removeEventListener('coze-image-uploaded', handleCozeImageUploaded as EventListener)
})
</script>

<style lang="scss" scoped>
@import 'tailwindcss';

.chron-engine-container {
  --message-spacing: 2rem;
}

/* 背景网格图案 */
.bg-grid-pattern {
  background-size: 40px 40px;
  background-image:
    linear-gradient(to right, var(--base-content) 1px, transparent 1px),
    linear-gradient(to bottom, var(--base-content) 1px, transparent 1px);
}
</style>
