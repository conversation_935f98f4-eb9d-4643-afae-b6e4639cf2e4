<template>
  <div class="h-full flex text-sm relative" :class="{ resizing: isResizing }">
    <!-- 主体内容区域 - 使用motion组件实现动画 -->
    <motion.div
      class="h-full overflow-x-hidden sidebar-content"
      :animate="{ width: isSidebarCollapsed ? 0 : sidebarWidth }"
      :transition="
        isResizing
          ? { duration: 0 }
          : {
              type: 'spring',
              stiffness: 500,
              damping: 50,
              mass: 0.6,
              restDelta: 0.1,
              restSpeed: 5
            }
      "
      :style="{ maxWidth: '350px' }"
    >
      <div class="h-full flex flex-col overflow-x-hidden">
        <!-- Header -->
        <div class="p-4 pb-0">
          <div class="flex items-center justify-between">
            <!-- AppLogo 下拉菜单 -->
            <DropdownMenu v-model:open="isAppMenuOpen">
              <DropdownMenuTrigger as-child>
                <div>
                  <AppLogo
                    :app-name="appConfig.name"
                    :logo="appConfig.logo"
                    :logo-color-class="appConfig.logoColor"
                    :show-dropdown="true"
                    :clickable="true"
                    :active="isAppMenuOpen"
                    @click="() => {}"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="bg-base-background border border-base-200 shadow-md rounded-md w-48 p-1.5"
                align="start"
                :no-close-animation="true"
              >
                <!-- 设置 -->
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                  @select="handleSettings"
                >
                  <div class="flex items-center justify-between w-full">
                    <span class="text-base-content">设置</span>
                  </div>
                </DropdownMenuItem>

                <!-- 检查更新 -->
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                  :class="{ 'opacity-50': isCheckingUpdate }"
                  :disabled="isCheckingUpdate"
                  @select="handleCheckForUpdates"
                >
                  <div class="flex items-center justify-between w-full">
                    <span class="text-base-content">{{
                      isCheckingUpdate ? '检查中...' : '检查更新'
                    }}</span>
                    <span class="text-base-content-tertiary text-xs">v{{ currentVersion }}</span>
                  </div>
                </DropdownMenuItem>

                <!-- 分割线 -->
                <div class="border-t border-base-200 my-1.5"></div>

                <!-- 登出 -->
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 h-8 flex items-center"
                  @select="handleLogout"
                >
                  <div class="flex items-center justify-between w-full">
                    <span class="text-base-content">登出</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div class="flex items-center gap-2">
              <!-- 搜索按钮 -->
              <button
                class="p-1 rounded-full hover:bg-base-100 text-base-content-secondary hover:text-base-content transition-colors duration-200"
                @click="$emit('toggle-search')"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2.5"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </button>
              <!-- 折叠按钮 - 只在展开状态显示 -->
              <button
                v-if="!isSidebarCollapsed"
                class="p-1 rounded-full hover:bg-base-100 transition-colors duration-200"
                @click="isSidebarCollapsed = true"
              >
                <ChevronLeft class="h-4 w-4 text-base-content" />
              </button>
            </div>
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex-1 p-4 space-y-6 flex flex-col overflow-hidden">
          <!-- Main Navigation -->
          <div class="space-y-2 flex-shrink-0">
            <NavigationItem
              v-for="item in mainNavigation"
              :key="item.id"
              v-bind="item"
              :badge="item.id === 'trash' ? trashStore.trashCount : item.badge"
              :active="activeItem === item.id"
              :toggleable="false"
              @navigate="handleNavigate"
            />
          </div>

          <!-- File Tree Section -->
          <div class="flex-1 flex flex-col min-h-0">
            <div
              class="flex items-center justify-between flex-shrink-0 mb-2"
              @mouseenter="isWorkspaceHeaderHovered = true"
              @mouseleave="isWorkspaceHeaderHovered = false"
            >
              <h3 class="select-none font-semibold text-subtitle uppercase tracking-wider">工作区</h3>
              <!-- Plus按钮 -->
              <motion.div
                :animate="{
                  opacity: isWorkspaceHeaderHovered || isPlusMenuOpen ? 1 : 0,
                  scale: isWorkspaceHeaderHovered || isPlusMenuOpen ? 1 : 0.8
                }"
                :transition="{
                  duration: 0.2,
                  ease: [0.25, 0.1, 0.25, 1.0]
                }"
                :style="{
                  pointerEvents: isWorkspaceHeaderHovered || isPlusMenuOpen ? 'auto' : 'none'
                }"
              >
                <DropdownMenu v-model:open="isPlusMenuOpen">
                  <DropdownMenuTrigger as-child>
                    <button
                      class="p-1 rounded-full text-base-content-secondary transition-colors duration-200"
                      :class="
                        isPlusMenuOpen
                          ? 'bg-base-100 text-base-content'
                          : 'hover:bg-base-100 hover:text-base-content'
                      "
                      @click.stop
                      title="添加新内容"
                    >
                      <Plus class="h-4 w-4" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    class="bg-base-background border border-base-200 shadow-md rounded-md"
                  >
                    <DropdownMenuItem
                      class="hover:bg-base-100 focus:bg-base-100"
                      @select="handleCreateNewNote"
                    >
                      <div class="flex items-center">
                        <FileTextIcon class="mr-2 h-3.5 w-3.5 text-indigo-500" />
                        <span class="text-base-content">新建笔记</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      class="hover:bg-base-100 focus:bg-base-100"
                      @select="handleImportPDF"
                    >
                      <div class="flex items-center">
                        <img src="@renderer/assets/images/reader.svg" class="mr-2 h-3.5 w-3.5" />
                        <span class="text-base-content">导入PDF</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      class="hover:bg-base-100 focus:bg-base-100"
                      @select="handleCreateTodoList"
                    >
                      <div class="flex items-center">
                        <ListTodo class="mr-2 h-3.5 w-3.5 text-green-500" />
                        <span class="text-base-content">新建待办清单</span>
                      </div>
                    </DropdownMenuItem>
                    <!-- 星流功能暂未完成，暂时隐藏
                    <DropdownMenuItem
                      class="hover:bg-base-100 focus:bg-base-100 cursor-pointer"
                      @select="handleCreateXingliu"
                    >
                      <div class="flex items-center">
                        <svg class="mr-2 h-3.5 w-3.5 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span class="text-base-content">新建星流</span>
                      </div>
                    </DropdownMenuItem>
                    -->
                  </DropdownMenuContent>
                </DropdownMenu>
              </motion.div>
            </div>

            <!-- 文件树内容区域 -->
            <div
              class="flex-1 mb-4 overflow-y-auto space-y-0.5 scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
            >
              <FileTreeNavigationItem
                v-for="node in nodeStore.visibleNodes"
                :key="node.uuid"
                :item="node"
                :level="0"
                :active-item="activeItem"
                class="font-medium"
              />
            </div>
          </div>

          <!-- Teams Section -->
        </div>
      </div>
    </motion.div>

    <!-- 展开按钮 - 在侧边栏折叠或宽度为0时显示 -->
    <motion.div
      v-if="(isSidebarCollapsed || sidebarWidth < 3) && !globalStore.isMessagePageOpen"
      class="z-30 absolute transform top-5 left-2"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :transition="{ duration: 1, ease: [0.25, 0.1, 0.25, 1.0], delay: 0.3 }"
    >
      <button
        class="p-1 rounded-full hover:bg-base-100 text-base-content-secondary hover:text-base-content transition-colors duration-200 ml-2"
        @click="handleExpandSidebar"
      >
        <ChevronRight class="h-4 w-4" />
      </button>
    </motion.div>

    <!-- 右侧边框区域 - 只在侧边栏展开时显示 -->
    <motion.div
      v-show="!isSidebarCollapsed"
      class="absolute top-0 right-0 z-30 w-[1px] bg-base-border h-full cursor-col-resize group hover:w-[2.5px] active:w-[2.5px] hover:bg-base-500 active:bg-base-500 transition-all duration-200"
      @mousedown="startResize"
      :initial="{ opacity: 1 }"
      :animate="{ opacity: isSidebarCollapsed ? 0 : 1 }"
      :transition="{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1.0] }"
    >
    </motion.div>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, onUnmounted } from 'vue'
import { motion } from 'motion-v'
import { ChevronLeft, ChevronRight, Plus, FileTextIcon, ListTodo } from 'lucide-vue-next'
import ChronEngineVideo from '../../assets/images/Drag.mp4'
import { AppLogo, NavigationItem, IconButton } from '../ui/v2'
import NavigationSection from './NavigationSection.vue'
// 导入文件树相关组件和状态管理
import FileTreeNavigationItem from './FileTreeNavigationItem.vue'
import { useFileTreeStore } from '../../stores/fileTreeStore'
import { useNodeStore } from '../../stores/nodeStore'
import type { NavigationItem as NavigationItemType } from '../../constants/navigation'
import {
  MAIN_NAVIGATION,
  WORKSPACE_NAVIGATION,
  TEAMS_NAVIGATION,
  TRY_NAVIGATION,
  APP_CONFIG,
  FOOTER_CONFIG
} from '../../constants/navigation'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '../ui/dropdown-menu'
// 导入创建功能相关的依赖
import { useBlockCreation } from '../../composables/useBlockCreation'
import { useTodoService } from '../../services/TodoService'
import { mitter } from '../../plugins/mitter'
// 导入功能介绍组件
import { useFeatureIntro } from '../../composables/useFeatureIntro'
// 导入路由相关依赖
import { useRouter } from 'vue-router'
import { useGlobalStore } from '../../stores/globalStore'
import { useUserStore } from '../../stores/user'
import { useTrashStore } from '../../stores/trashStore'
import { usePanelStore } from '../../stores/panelStore'
import { getPlatform } from '../../utils/platform'
import { showToast } from '../../utils/toast'
import { track } from '../../utils/tracks'
import { setCurrentResource } from '../../utils/bus'

interface Props {
  activeItem: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'navigate', item: NavigationItemType): void
  (e: 'toggle-search'): void
}>()

// 配置数据
const appConfig = APP_CONFIG
const footerConfig = FOOTER_CONFIG
const mainNavigation = MAIN_NAVIGATION
const workspaceSection = WORKSPACE_NAVIGATION
const teamsSection = TEAMS_NAVIGATION
const trySection = TRY_NAVIGATION

// 文件树相关状态管理
const fileTreeStore = useFileTreeStore()
const nodeStore = useNodeStore()

// 创建功能相关的composables
const { createBlock } = useBlockCreation()
const todoService = useTodoService()
const { show: showFeatureIntro } = useFeatureIntro()

// 路由相关
const router = useRouter()
const globalStore = useGlobalStore()
const userStore = useUserStore()
const trashStore = useTrashStore()
const panelStore = usePanelStore()
const platform = getPlatform()

// 侧边栏折叠状态
const isSidebarCollapsed = ref(false)

// DropdownMenu 打开状态
const isAppMenuOpen = ref(false)
const isPlusMenuOpen = ref(false)

// 工作区 header hover 状态
const isWorkspaceHeaderHovered = ref(false)

// 检查更新相关状态
const isCheckingUpdate = ref(false)
const currentVersion = ref('1.0.0')

// 添加侧边栏宽度相关的状态
const sidebarWidth = ref(256) // 默认宽度 (w-64 = 256px)
const minWidth = 0 // 最小宽度
const maxWidth = 350 // 最大宽度
let isResizing = ref(false)
let rafId: number | null = null

// 声明事件处理函数
let handleMouseMove: (e: MouseEvent) => void
let handleMouseUp: () => void

// 展开侧边栏
const handleExpandSidebar = () => {
  if (isSidebarCollapsed.value) {
    isSidebarCollapsed.value = false
  } else if (sidebarWidth.value < 3) {
    // 如果宽度小于3，恢复到默认宽度
    sidebarWidth.value = 256
  }
}

// 开始拖拽
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  const startX = e.clientX
  const startWidth = sidebarWidth.value

  // 添加一个类到body，防止文本选择和其他干扰
  document.body.classList.add('sidebar-resizing')

  handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.value) return

    // 取消之前的动画帧
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
    }

    // 使用 requestAnimationFrame 优化性能
    rafId = requestAnimationFrame(() => {
      const delta = e.clientX - startX
      let newWidth = startWidth + delta

      // 限制最小和最大宽度
      newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))

      // 直接设置DOM元素的宽度，跳过Vue的响应式系统和动画
      const sidebarEl = document.querySelector('.sidebar-content') as HTMLElement
      if (sidebarEl) {
        sidebarEl.style.width = `${newWidth}px`
      }

      // 只在鼠标释放后更新响应式变量，这样不会触发动画
      sidebarWidth.value = newWidth
    })
  }

  handleMouseUp = () => {
    isResizing.value = false
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
      rafId = null
    }

    // 移除body上的类
    document.body.classList.remove('sidebar-resizing')

    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 在组件卸载时清理
onUnmounted(() => {
  if (isResizing.value) {
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
    }
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
})

// 处理函数
const handleCreateNewNote = () => {
  createBlock('note') // 会自动打开新创建的笔记
}

const handleImportPDF = () => {
  createBlock('pdf') // 会自动打开新创建的PDF
}

const handleCreateTodoList = async () => {
  try {
    const listUuid = await todoService.createTodoList('新建待办清单')
    mitter.emit('note-created', {
      noteId: listUuid,
      parentId: null,
      shouldScroll: true
    })

    // 自动在面板中打开新创建的待办清单
    const resourceNode = nodeStore.getNodeById(listUuid) as any

    if (resourceNode) {
      // 使用 dockview 系统打开新待办清单
      const { useDockviewStore } = await import('../../stores/dockviewStore')
      const dockviewStore = useDockviewStore()
      
      const panel = dockviewStore.addPanel({
        id: `todo-${listUuid}`,
        title: resourceNode.title || '新建待办清单',
        component: 'panel',
        params: {
          type: 'todo',
          node: resourceNode
        }
      })
      
      if (panel) {
        dockviewStore.setActivePanelId(panel.id)
      }
    }
  } catch (error) {
    console.error('创建待办清单失败:', error)
  }
}

// 星流功能暂未完成，暂时注释
// const handleCreateXingliu = () => {
//   createBlock('xingliu')
// }

// AppLogo 下拉菜单处理函数
const handleSettings = () => {
  globalStore.isSettingsOpen = true
}

const handleCheckForUpdates = async () => {
  track({
    action_type: 'Check updates'
  })
  if (platform === 'electron' && !isCheckingUpdate.value) {
    isCheckingUpdate.value = true
    try {
      const hasUpdate = await (window as any).electron.ipcRenderer.invoke('check-for-updates')
      if (hasUpdate) {
        showToast('检查更新', '发现新版本，请稍后安装', 2000)
        globalStore.isUpdaterModalOpen = true
      } else {
        showToast('检查更新', '当前已是最新版本', 2000)
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      showToast('检查更新', '检查更新失败，请稍后重试', 2000)
    } finally {
      isCheckingUpdate.value = false
    }
  }
}

const handleLogout = async () => {
  console.log('登出')
  // 添加小延迟确保下拉菜单关闭动画完成
  setTimeout(async () => {
    // 清空所有localStorage数据
    localStorage.clear()

    // 清空用户信息
    userStore.clearUserInfo()

    // 重置面板状态
    try {
      // 尝试清理面板状态
      const { useDockviewStore } = await import('../../stores/dockviewStore')
      const dockviewStore = useDockviewStore()
      dockviewStore.closeAllPanels()
    } catch (error) {
      console.log('清理面板状态时出错:', error)
    }

    setCurrentResource('', '') // 清空 current_resource

    // 通知主进程进行窗口调整
    if (platform === 'electron') {
      ;(window as any).electron.ipcRenderer.send('hidden-window')
      await router.push('/login')
      ;(window as any).electron.ipcRenderer.send('logout')
      return
    }

    await router.push('/login')
  }, 150)
}

// 处理导航事件
const handleNavigate = (item: any) => {
  // 关闭其他页面状态
  if (item.id !== 'mail') {
    globalStore.isMessagePageOpen = false
  }
  if (item.id !== 'trash') {
    globalStore.isTrashOpen = false
  }

  if (item.id === 'chronengine') {
    // 处理 Chron Engine 点击事件 - 打开引擎
    handleOpenChronEngine()
  } else if (item.id === 'mail') {
    // 处理消息导航 - 打开消息页面
    handleOpenMail()
  } else if (item.id === 'trash') {
    // 处理回收站导航 - 打开回收站页面
    handleOpenTrash()
  } else {
    // 其他导航项正常处理
    emit('navigate', item as NavigationItemType)
  }
}

// 打开 Chron Engine
const handleOpenChronEngine = async () => {
  // 先显示功能介绍弹窗
  const wasShown = await showFeatureIntro({
    id: 'chronengine-intro-v1',
    gifUrl: ChronEngineVideo,
    content: () => (
      <div>
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          marginBottom: '0.5rem',
          color: '#FFFFFF',
          lineHeight: '1.2'
        }}>
          Chron引擎随心拖拽
        </h1>
        <p style={{
          color: '#9CA3AF',
          marginBottom: '1.5rem',
          fontSize: '1rem',
          lineHeight: '1.5'
        }}>
          随手拖拽Chron引擎至任意位置，AI用起来超方便！
        </p>
        
        <div style={{ marginBottom: '1.5rem' }}>
          <ul style={{
            listStyle: 'disc',
            paddingLeft: '1.5rem',
            color: '#FFFFFF'
          }}>
            <li style={{ marginBottom: '0.8rem' }}>
              随心拖动Chorn引擎，界面随你定
            </li>
            <li style={{ marginBottom: '0.8rem' }}>
              探索笔记、PDF与Chorn引擎的智能互动，解锁高效体验！
            </li>
            <li>
              一键分屏，打造专属高效工作流
            </li>
          </ul>
        </div>
      </div>
    ),
    buttonText: '我已知晓',
    onAcknowledge: () => {
      console.log('用户确认了 ChronEngine 介绍')
      track({
        action_type: 'Acknowledge ChronEngine Intro'
      })
    }
  })

  // 打开 ChronEngine 面板（无论是否显示了介绍都要打开）
  const { useDockviewStore } = await import('../../stores/dockviewStore')
  const dockviewStore = useDockviewStore()
  
  const chronEngineId = 'chronengine-' + Date.now()
  
  const panel = dockviewStore.addPanel({
    id: `chronengine-${chronEngineId}`,
    title: 'Chron Engine',
    component: 'panel',
    params: {
      type: 'chronengine',
      node: {
        uuid: chronEngineId,
        type: 'chronengine',
        title: 'Chron Engine'
      }
    }
  })
  
  if (panel) {
    dockviewStore.setActivePanelId(panel.id)
  }

  track({
    action_type: 'Open ChronEngine from Sidebar',
    wasIntroShown: wasShown
  })
}

// 打开消息页面
const handleOpenMail = () => {
  globalStore.isMessagePageOpen = true
  track({
    action_type: 'Open Mail from Sidebar'
  })
}

// 打开回收站页面
const handleOpenTrash = () => {
  trashStore.openTrash()
  track({
    action_type: 'Open Trash from Sidebar'
  })
}

// 获取应用版本
onMounted(async () => {
  if (platform === 'electron') {
    try {
      currentVersion.value = await (window as any).electron.ipcRenderer.invoke('get-app-version')
    } catch (error) {
      console.error('获取版本信息失败:', error)
    }
  }
})
</script>

<style scoped>
/* 添加硬件加速和性能优化相关样式 */
.sidebar-content {
  transform: translateZ(0);
  will-change: width;
  backface-visibility: hidden;
  contain: layout style;
  -webkit-font-smoothing: subpixel-antialiased;
  transition: none;
}

/* 优化动画性能 */
motion.div {
  contain: layout style;
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 拖动时禁用文本选择和优化性能 */
.resizing {
  user-select: none;
  -webkit-user-select: none;
  cursor: col-resize !important;
}

/* 全局拖动状态 */
.sidebar-resizing {
  cursor: col-resize !important;
  user-select: none;
  -webkit-user-select: none;
}

/* 拖动时禁用动画 */
.sidebar-resizing * {
  transition: none !important;
  animation: none !important;
}

/* 确保所有内容在宽度不足时不会导致横向滚动 */
:deep(.h-full),
:deep(.w-full) {
  max-width: 100%;
  box-sizing: border-box;
}
</style>
