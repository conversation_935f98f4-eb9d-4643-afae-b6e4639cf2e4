<template>
  <div 
    class="w-full h-full relative"
    @drop.stop="handleDrop"
    @dragover.stop="handleDragOver"
    @dragenter.stop="handleDragEnter"
    @dragleave.stop="handleDragLeave"
  >
    <!-- 拖拽指示器 -->
    <div
      v-if="isDragOver"
      class="absolute z-[999] inset-0 pointer-events-none drop-indicator-container"
    >
      <!-- 上方区域 -->
      <div class="drop-area top-area" :class="{ active: dropPosition === 'top' }">
        <div class="drop-area-inner"></div>
        <div v-if="dropPosition === 'top'" class="drop-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-6 h-6"
          >
            <path
              fill-rule="evenodd"
              d="M11.47 4.72a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 0 1-1.06 1.06L12 6.31 8.78 9.53a.75.75 0 0 1-1.06-1.06l3.75-3.75Z"
              clip-rule="evenodd"
            />
            <path
              fill-rule="evenodd"
              d="M12 6.75a.75.75 0 0 1 .75.75v9a.75.75 0 0 1-1.5 0v-9a.75.75 0 0 1 .75-.75Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="drop-area right-area" :class="{ active: dropPosition === 'right' }">
        <div class="drop-area-inner"></div>
        <div v-if="dropPosition === 'right'" class="drop-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-6 h-6"
          >
            <path
              fill-rule="evenodd"
              d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>

      <!-- 下方区域 -->
      <div class="drop-area bottom-area" :class="{ active: dropPosition === 'bottom' }">
        <div class="drop-area-inner"></div>
        <div v-if="dropPosition === 'bottom'" class="drop-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-6 h-6"
          >
            <path
              fill-rule="evenodd"
              d="M12 3.75a.75.75 0 0 1 .75.75v9a.75.75 0 0 1-1.5 0v-9a.75.75 0 0 1 .75-.75Z"
              clip-rule="evenodd"
            />
            <path
              fill-rule="evenodd"
              d="M6.31 12.78a.75.75 0 0 1 1.06-1.06l4.63 4.63 4.63-4.63a.75.75 0 0 1 1.06 1.06l-5.16 5.16a.75.75 0 0 1-1.06 0l-5.16-5.16Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>

      <!-- 左侧区域 -->
      <div class="drop-area left-area" :class="{ active: dropPosition === 'left' }">
        <div class="drop-area-inner"></div>
        <div v-if="dropPosition === 'left'" class="drop-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-6 h-6"
          >
            <path
              fill-rule="evenodd"
              d="M11.03 3.97a.75.75 0 0 1 0 1.06l-6.22 6.22H21a.75.75 0 0 1 0 1.5H4.81l6.22 6.22a.75.75 0 1 1-1.06 1.06l-7.5-7.5a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 0 1 1.06 0Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>

      <!-- 中心区域 -->
      <div class="drop-area center-area" :class="{ active: dropPosition === 'center' }">
        <div class="drop-area-inner"></div>
        <div v-if="dropPosition === 'center'" class="drop-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-6 h-6"
          >
            <path
              fill-rule="evenodd"
              d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm0 8.625a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25ZM15.375 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm-8.25 0a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="w-full h-full relative">
      <template v-if="params.params.type === 'empty'">
        <Empty class="w-full h-full" />
      </template>
      <template v-else-if="params.params.type === 'note'">
        <NoteEditor
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      <template v-else-if="params.params.type === 'pdf'">
        <PDFViewer
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      <template v-else-if="params.params.type === 'xingliu'">
        <XingliuCanvas
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      <template v-else-if="params.params.type === 'chronengine'">
        <ChronEngineComponent
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      <template v-else-if="params.params.type === 'todo'">
        <TodoListPanel
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      <template v-else>
        <!-- 默认回退到 NoteEditor -->
        <NoteEditor
          :key="componentKey"
          class="w-full h-full"
          :node="node"
          :panel_id="params.api.id"
          @title-changed="handleTitleChanged"
        />
      </template>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { IDockviewPanelProps } from 'dockview-vue'
import { computed, PropType, ref } from 'vue'
import { BaseNode } from '@renderer/componentSystem/common/BaseNode'
import { mitter } from '@renderer/plugins/mitter'
import { useNodeStore } from '@renderer/stores/nodeStore'
import { useDockviewStore } from '@renderer/stores/dockviewStore'
import { v4 } from 'uuid'
import NoteEditor from '../Editor/NoteEditor.vue'
import PDFViewer from '../PDF/PDFViewer.vue'
import XingliuCanvas from '../Xingliu/XingliuCanvas.vue'
import ChronEngineComponent from '../ChronEngine/ChronEngineComponent.vue'
import TodoListPanel from '../Todo/TodoListPanel.vue'
import Empty from './Empty.vue'

const props = defineProps({
  params: {
    type: Object as PropType<IDockviewPanelProps>,
    required: true
  }
})

const componentKey = computed(() => props.params.params.node?.uuid || props.params.api.id)
const node = ref(props.params.params.node)

// 拖拽相关状态
const isDragOver = ref(false)
const dropPosition = ref<'top' | 'right' | 'bottom' | 'left' | 'center'>('center')

props.params.api.onDidParametersChange(() => {
  node.value = props.params.params.node
  console.log(node.value)
})


const handleTitleChanged = (title: string) => {
  console.log('handleTitleChanged', title)
  props.params.api.setTitle(title)
}

// 确定拖拽位置
const determineDropPosition = (e: DragEvent, element: HTMLElement) => {
  const rect = element.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top
  const width = rect.width
  const height = rect.height

  // 中心区域的大小（占总尺寸的百分比）
  const centerThreshold = 0.3
  const centerWidth = width * centerThreshold
  const centerHeight = height * centerThreshold
  const centerLeft = width / 2 - centerWidth / 2
  const centerTop = height / 2 - centerHeight / 2
  const centerRight = centerLeft + centerWidth
  const centerBottom = centerTop + centerHeight

  // 检查是否在中心区域
  if (x >= centerLeft && x <= centerRight && y >= centerTop && y <= centerBottom) {
    return 'center'
  }

  // 确定最近的边缘
  const distToLeft = x
  const distToRight = width - x
  const distToTop = y
  const distToBottom = height - y

  const minDist = Math.min(distToLeft, distToRight, distToTop, distToBottom)

  if (minDist === distToLeft) return 'left'
  if (minDist === distToRight) return 'right'
  if (minDist === distToTop) return 'top'
  return 'bottom'
}

// 拖拽处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  if (e.dataTransfer?.effectAllowed == 'copyMove') {
    isDragOver.value = false
    return
  }
  if (e.dataTransfer?.effectAllowed === 'all') {
    isDragOver.value = false
    return
  }

  if (e.dataTransfer && e.dataTransfer.types.includes('application/json')) {
    // 检查是否是PDF注释拖拽（application/json类型）
    isDragOver.value = false
    return
  }

  // 检查是否是 kanban 拖拽（只有 text/plain 而没有 id 和 type）
  if (
    e.dataTransfer &&
    e.dataTransfer.types.includes('text/plain') &&
    !e.dataTransfer.types.includes('id') &&
    !e.dataTransfer.types.includes('type')
  ) {
    isDragOver.value = false
    return
  }

  if (!isDragOver.value) return

  // 确定拖拽位置
  dropPosition.value = determineDropPosition(e, e.currentTarget as HTMLElement)
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()

  // 检查是否是PDF注释拖拽（application/json类型）
  if (e.dataTransfer && e.dataTransfer.types.includes('application/json')) {
    isDragOver.value = false
    return
  }

  // 检查是否是 kanban 拖拽（只有 text/plain 而没有 id 和 type）
  if (
    e.dataTransfer &&
    e.dataTransfer.types.includes('text/plain') &&
    !e.dataTransfer.types.includes('id') &&
    !e.dataTransfer.types.includes('type')
  ) {
    isDragOver.value = false
    return
  }

  // 对于其他类型的拖拽（如资源节点），显示分屏提示
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  // 检查是否真的离开了元素（而不是进入了子元素）
  const relatedTarget = e.relatedTarget as HTMLElement
  if (relatedTarget && (e.currentTarget as HTMLElement).contains(relatedTarget)) {
    return
  }
  isDragOver.value = false
}

const handleDrop = async (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()

  // 检查是否是PDF注释拖拽（application/json类型）
  if (e.dataTransfer && e.dataTransfer.types.includes('application/json')) {
    isDragOver.value = false
    return
  }

  // 检查是否是 kanban 拖拽（只有 text/plain 而没有 id 和 type）
  if (
    e.dataTransfer &&
    e.dataTransfer.types.includes('text/plain') &&
    !e.dataTransfer.types.includes('id') &&
    !e.dataTransfer.types.includes('type')
  ) {
    isDragOver.value = false
    return
  }

  const dragId = e.dataTransfer?.getData('id')
  const dragType = e.dataTransfer?.getData('type')

  if (!dragId || !dragType) return

  isDragOver.value = false
  // 发送拖拽结束事件
  mitter.emit('splitpanel:dragend')

  const finalPosition = determineDropPosition(e, e.currentTarget as HTMLElement)
  let resourceNode = useNodeStore().getNodeById(dragId) as BaseNode

  // 如果是 ChronEngine 类型，创建虚拟节点
  if (!resourceNode && dragType === 'chronengine') {
    const { BlockJsonNode } = await import('@renderer/componentSystem/BlockJsonNode')
    resourceNode = new BlockJsonNode({
      uuid: dragId,
      type: 'chronengine',
      title: 'Chron Engine',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      meta: [],
      parent: null,
      children: []
    })
  }

  if (!resourceNode) return

  const dockviewStore = useDockviewStore()

  // 确定面板类型
  const panelType = resourceNode.type || 'note'

  // 如果是空面板，直接更新内容
  if (props.params.params.type === 'empty') {
    // 更新当前面板的参数
    props.params.api.updateParameters({
      type: panelType,
      node: resourceNode
    })
    // 更新面板标题
    props.params.api.setTitle(resourceNode.title)
    return
  }

  // 根据拖拽位置决定是替换还是分屏
  if (finalPosition === 'center') {
    // 靠近中心，替换当前面板
    props.params.api.updateParameters({
      type: panelType,
      node: resourceNode
    })
    props.params.api.setTitle(resourceNode.title)
  } else {
    // 靠近边缘，进行分屏
    const newPanelId = `panel-${v4()}`
    
    let direction: 'left' | 'right' | 'above' | 'below'
    if (finalPosition === 'left') {
      direction = 'left'
    } else if (finalPosition === 'right') {
      direction = 'right'
    } else if (finalPosition === 'top') {
      direction = 'above'
    } else {
      direction = 'below'
    }

    // 使用 dockview API 创建新面板
    dockviewStore.addPanel({
      id: newPanelId,
      title: resourceNode.title,
      component: 'panel',
      params: {
        type: panelType,
        node: resourceNode
      },
      position: {
        direction,
        referencePanel: props.params.api.id
      }
    })
  }
}
</script>

<style scoped>
/* 拖拽指示器动画 */
.drop-indicator-container {
  animation: fadeIn 0.2s ease-out;
}

/* 拖拽区域基础样式 */
.drop-area {
  position: absolute;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 拖拽区域内部样式 */
.drop-area-inner {
  position: absolute;
  inset: 0;
  background: rgba(99, 102, 241, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.15);
  opacity: 0.7;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 激活状态的拖拽区域 */
.drop-area.active .drop-area-inner {
  background: rgba(99, 102, 241, 0.15);
  border: 2px solid rgba(99, 102, 241, 0.3);
  opacity: 1;
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.1);
}

/* 拖拽图标样式 */
.drop-icon {
  color: rgba(99, 102, 241, 1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  z-index: 1;
  animation: bounceIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 区域位置定义 - 增加间距避免重叠 */
.top-area {
  top: 12px;
  left: 20px;
  right: 20px;
  height: calc(40% - 16px);
}

.right-area {
  top: 20px;
  right: 12px;
  bottom: 20px;
  width: calc(40% - 16px);
}

.bottom-area {
  bottom: 12px;
  left: 20px;
  right: 20px;
  height: calc(40% - 16px);
}

.left-area {
  top: 20px;
  left: 12px;
  bottom: 20px;
  width: calc(40% - 16px);
}

.center-area {
  top: 30%;
  left: 30%;
  right: 30%;
  bottom: 30%;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .drop-area-inner {
    background: rgba(99, 102, 241, 0.12);
    border: 1px solid rgba(99, 102, 241, 0.25);
  }

  .drop-area.active .drop-area-inner {
    background: rgba(99, 102, 241, 0.2);
    border: 2px solid rgba(99, 102, 241, 0.4);
    box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.15);
  }

  .drop-icon {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(99, 102, 241, 1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  }
}
</style>
