<template>
  <!-- 简洁背景 -->
  <div class="absolute inset-0 bg-base-100"></div>

  <!-- 登录卡片 -->
  <div
    @mousedown.stop
    class="w-full h-full p-8 relative"
    :style="
      'transform: translateZ(0); --webkit-scrollbar: none; ' +
      (globalStore.isUpdaterModalOpen ? '' : ' -webkit-app-region: drag;')
    "
  >
    <!-- 自定义标题栏 -->
    <div
      class="absolute top-0 left-0 right-0 h-8 flex justify-end items-center z-[100]"
      style="-webkit-app-region: drag"
    >
      <!-- 右侧窗口控制按钮 (仅Windows显示) -->
      <div class="flex h-full z-[100]" v-if="isWindows">
        <button
          @click="minimizeWindow"
          style="-webkit-app-region: no-drag"
          class="w-10 h-8 flex items-center justify-center text-base-content/50 hover:text-base-content hover:bg-base-300 transition-colors"
        >
          <div class="w-3 h-0.5 bg-current"></div>
        </button>
        <button
          @click="closeWindow"
          style="-webkit-app-region: no-drag"
          class="w-10 h-8 flex items-center justify-center text-base-content/50 hover:text-error hover:bg-base-300 transition-colors"
        >
          <svg
            class="w-3.5 h-3.5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <path d="M18 6L6 18M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Logo 区域 -->
    <div class="relative flex justify-center mb-8">
      <!-- Logo光晕效果 -->
      <div
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-full blur-xl animate-pulse will-change-transform"
        style="transform: translateZ(0); contain: layout style paint"
      ></div>

      <!-- Logo -->
      <img
        class="w-16 select-none transition-all duration-300 hover:scale-110 drop-shadow-md z-10 relative will-change-transform"
        draggable="false"
        src="@renderer/assets/images/icon.png"
        alt="Chronnote"
      />
    </div>

    <!-- 标题区 -->
    <div
      class="select-none flex justify-between items-center mb-7 relative z-10"
      style="-webkit-app-region: no-drag"
    >
      <h1 class="text-lg font-bold text-base-content">登录</h1>
      <div class="text-xs text-base-content/60">
        没有账号？
        <span
          class="font-medium text-primary hover:text-primary-focus transition-colors cursor-pointer"
          @click="goToRegister"
          >现在注册</span
        >
      </div>
    </div>

    <!-- 表单 -->
    <form @submit.prevent="submitForm" class="space-y-4 relative z-10">
      <!-- 邮箱/手机号输入 -->
      <div>
        <div class="relative group">
          <input
            v-model="email"
            type="text"
            v-bind="emailAttrs"
            placeholder="请输入手机号/邮箱"
            class="w-full px-4 py-2.5 bg-base-100 border border-base-content/30 rounded-xl text-sm text-base-content outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 placeholder:text-base-content/40 group-hover:border-base-content/50"
            style="-webkit-app-region: no-drag"
            @keydown="handleInputKeydown"
          />
        </div>
        <div class="h-5 mt-0.5">
          <p v-if="errors.email" class="text-xs text-error pl-1">{{ errors.email }}</p>
        </div>
      </div>

      <!-- 密码输入 -->
      <div>
        <div class="relative group">
          <input
            v-model="password"
            v-bind="passwordAttrs"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入密码"
            class="w-full px-4 py-2.5 bg-base-100 border border-base-content/30 rounded-xl text-sm text-base-content outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 placeholder:text-base-content/40 group-hover:border-base-content/50"
            style="-webkit-app-region: no-drag"
            @keydown="handleInputKeydown"
          />
          <button
            type="button"
            class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/40 hover:text-base-content/60 transition-colors"
            @click="showPassword = !showPassword"
            style="-webkit-app-region: no-drag"
          >
            <EyeIcon v-if="!showPassword" class="w-4 h-4" />
            <EyeClosedIcon v-else class="w-4 h-4" />
          </button>
        </div>
        <div class="h-5 mt-0.5 flex justify-between">
          <p v-if="errors.password" class="text-xs text-error pl-1">{{ errors.password }}</p>
          <span
            class="text-xs text-primary hover:text-primary-focus cursor-pointer transition-colors"
            @click="showForgotPasswordModal = true"
            style="-webkit-app-region: no-drag"
          >
            忘记密码？
          </span>
        </div>
      </div>

      <!-- 同意协议 -->
      <div style="-webkit-app-region: no-drag">
        <label class="flex items-center gap-2 cursor-pointer group">
          <div class="relative flex items-center justify-center">
            <input
              v-model="rememberMe"
              type="checkbox"
              v-bind="rememberMeAttrs"
              class="appearance-none w-3.5 h-3.5 border border-base-content/40 rounded transition-colors duration-200 checked:bg-primary checked:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 group-hover:border-base-content/60"
              style="-webkit-app-region: no-drag"
            />
            <svg
              class="absolute w-2.5 h-2.5 text-base-100 pointer-events-none opacity-0 transition-opacity duration-200"
              :class="{ 'opacity-100': rememberMe }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
          </div>
          <div>
            <span
              class="text-xs text-base-content/60 group-hover:text-base-content/70 transition-colors select-none"
              >我已阅读并同意</span
            >
            <span
              class="text-xs font-medium text-primary hover:text-primary-focus transition-colors"
              >用户协议</span
            >
          </div>
        </label>
        <div class="h-5 mt-0.5">
          <p v-if="errors.rememberMe" class="text-xs text-error pl-1">{{ errors.rememberMe }}</p>
        </div>
      </div>

      <!-- 错误信息显示区域 -->
      <div
        v-if="errorMessage"
        class="p-2 bg-error/10 border border-error/20 rounded-lg"
        style="-webkit-app-region: no-drag"
      >
        <p class="text-xs text-error">{{ errorMessage }}</p>
      </div>

      <!-- 登录按钮 -->
      <button
        type="submit"
        class="w-full px-4 py-2.5 mt-2 bg-primary text-primary-content text-sm font-medium rounded-xl transition-all duration-200 hover:bg-primary-focus"
        :disabled="isLoading"
        style="-webkit-app-region: no-drag"
      >
        <div class="flex items-center justify-center">
          <span v-if="isLoading" class="flex items-center">
            <svg
              class="animate-spin -ml-1 mr-2 h-3.5 w-3.5 text-primary-content"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            登录中...
          </span>
          <span v-else>登录</span>
        </div>
      </button>
    </form>

    <!-- 分隔线 -->
    <div class="my-6 flex items-center">
      <div class="flex-grow h-px bg-base-300"></div>
      <span class="px-3 text-xs text-base-content/40 font-light">或</span>
      <div class="flex-grow h-px bg-base-300"></div>
    </div>

    <!-- 页脚 -->
    <div class="text-center relative z-10" style="-webkit-app-region: no-drag">
      <span class="text-xs text-base-content/60">有问题？请联系邮箱</span>
      <a
        href="mailto:<EMAIL>"
        class="text-xs font-medium text-primary hover:text-primary-focus transition-colors ml-1"
        ><EMAIL></a
      >
    </div>
  </div>

  <!-- 忘记密码模态框 -->
  <ForgotPasswordModal
    v-if="showForgotPasswordModal"
    @close="showForgotPasswordModal = false"
    @handle-input-keydown="handleInputKeydown"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { login } from '@renderer/api/uniform/auth'
import { useUserStore } from '@renderer/stores/user'
import { EyeClosedIcon, EyeIcon } from 'lucide-vue-next'
import { emailRegex, phoneRegex } from '@renderer/utils/regex'
import { InitializationService } from '@renderer/services/InitializationService'
import { getPlatform } from '@renderer/utils/platform'
import * as yup from 'yup'
import { useGlobalStore } from '@renderer/stores/globalStore'
import ForgotPasswordModal from './ForgotPasswordModal.vue'

const router = useRouter()
const platform = getPlatform()
const isWindows = ref(platform === 'electron' && navigator.platform.toLowerCase().includes('win'))
const globalStore = useGlobalStore()

// 窗口控制函数
const minimizeWindow = () => {
  if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
    const isQuickNoteWindow = window.location.hash.includes('quick-note')
    if (isQuickNoteWindow) {
      ;(window as any).electron.ipcRenderer.send('minimize-quick-note-window')
    } else {
      ;(window as any).electron.ipcRenderer.send('window-minimize')
    }
    console.log('发送最小化窗口事件')
  }
}

const closeWindow = () => {
  if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
    const isQuickNoteWindow = window.location.hash.includes('quick-note')
    if (isQuickNoteWindow) {
      ;(window as any).electron.ipcRenderer.send('close-quick-note-window')
    } else {
      ;(window as any).electron.ipcRenderer.send('window-close')
    }
    console.log('发送关闭窗口事件')
  }
}

// 组件挂载时设置窗口大小为登录窗口大小
onMounted(() => {
  if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
    ;(window as any).electron.ipcRenderer.send('set-login-window-size')
    ;(window as any).electron.ipcRenderer.send('check-opacity')
  }
})

// 表单验证规则
const { errors, handleSubmit, defineField } = useForm({
  validationSchema: yup.object({
    email: yup
      .string()
      .required('手机号或邮箱不能为空')
      .test('email-or-phone', '请输入正确的手机号或邮箱', (value) => {
        if (!value) return false
        return emailRegex.test(value) || phoneRegex.test(value)
      }),
    password: yup
      .string()
      .required('密码不能为空')
      .min(6, '密码至少需要 6 个字符')
      .max(20, '密码最多只能有 20 个字符'),
    rememberMe: yup.boolean().equals([true], '请同意用户协议').required('请确认是否同意用户协议')
  })
})

const [email, emailAttrs] = defineField('email')
const [password, passwordAttrs] = defineField('password')
const [rememberMe, rememberMeAttrs] = defineField('rememberMe')

const showPassword = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const showForgotPasswordModal = ref(false)

const goToRegister = () => {
  if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
    ;(window as any).electron.ipcRenderer.send('switch-login-register', false)
  }
  router.push('/register')
}

const submitForm = handleSubmit(async (_values) => {
  errorMessage.value = ''
  try {
    isLoading.value = true
    let flag: number = 0
    if (emailRegex.test(email.value)) {
      flag = 1
    } else if (phoneRegex.test(email.value)) {
      flag = 2
    } else {
      return
    }

    try {
      const res = await login(email.value, password.value, flag)
      if (res.data.code === 0) {
        const userStore = useUserStore()
        if (res.data.data?.token) {
          userStore.setToken(res.data.data.token)
          await userStore.setUserInfo({
            email: res.data.data.email,
            uuid: res.data.data.uuid
          })

          try {
            await InitializationService.initUserData()
          } catch (error) {
            console.error('Failed to initialize user data:', error)
            // 如果初始化失败，显示错误信息
            errorMessage.value = '数据初始化失败，请重试'
            return
          }

          if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
            ;(window as any).electron.ipcRenderer.send('hidden-window')
          }
          await router.push('/')
          if (platform === 'electron' && (window as any).electron?.ipcRenderer) {
            ;(window as any).electron.ipcRenderer.send('login-success')
            ;(window as any).electron.ipcRenderer.send('set-main-window-size')
          }
        } else {
          errorMessage.value = '登录响应缺少令牌信息'
          console.error('Login response missing token:', res.data)
        }
      } else {
        errorMessage.value = res.data.message || '登录失败，请检查您的凭据'
      }
    } catch (error) {
      console.error('Login error:', error)
      errorMessage.value = '网络错误，请检查您的网络连接并重试'
    }
  } finally {
    isLoading.value = false
  }
})

// 输入框全选处理函数
const handleInputKeydown = (e: KeyboardEvent) => {
  if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
    e.preventDefault()
    const target = e.target as HTMLInputElement
    target.select()
  }
}
</script>

<style scoped>
/* Electron 平台特定样式 */
</style>
