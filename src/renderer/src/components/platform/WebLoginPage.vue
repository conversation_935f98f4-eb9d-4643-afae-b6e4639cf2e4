<template>
  <!-- Web 登录页面 -->
  <div class="min-h-screen flex">
    <!-- 左侧装饰区域 -->
    <div class="hidden lg:flex lg:w-1/2 bg-white relative border-r border-gray-100">
      <!-- 左侧内容 -->
      <div class="flex flex-col justify-center items-start p-20 w-full">
        <div class="max-w-xs">
          <!-- Logo 区域 -->
          <div class="mb-16">
            <img class="w-10 h-10 mb-8" src="@renderer/assets/images/icon.png" alt="Chronnote" />
            <h1 class="text-2xl font-light text-black mb-6">Chronnote</h1>
            <p class="text-base text-gray-700 leading-relaxed">智能笔记管理平台</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
      <div class="w-full max-w-md">
        <!-- Logo 区域 (移动端显示) -->
        <div class="lg:hidden text-center mb-8">
          <img
            class="w-16 h-16 mx-auto mb-4"
            src="@renderer/assets/images/icon.png"
            alt="Chronnote"
          />
          <h1 class="text-2xl font-bold text-gray-900">Chronnote</h1>
        </div>

        <!-- 登录表单卡片 -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
          <!-- 标题 -->
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">登录账户</h2>
            <p class="text-gray-600">请输入您的登录信息</p>
          </div>

          <!-- 表单 -->
          <form @submit.prevent="submitForm" class="space-y-6">
            <!-- 邮箱/手机号输入 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">邮箱或手机号</label>
              <input
                v-model="email"
                type="text"
                v-bind="emailAttrs"
                placeholder="请输入手机号或邮箱"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                @keydown="handleInputKeydown"
              />
              <div class="h-5 mt-1">
                <p v-if="errors.email" class="text-xs text-red-500">{{ errors.email }}</p>
              </div>
            </div>

            <!-- 密码输入 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <div class="relative">
                <input
                  v-model="password"
                  v-bind="passwordAttrs"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                  @keydown="handleInputKeydown"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  @click="showPassword = !showPassword"
                >
                  <EyeIcon v-if="!showPassword" class="w-5 h-5" />
                  <EyeClosedIcon v-else class="w-5 h-5" />
                </button>
              </div>
              <div class="h-5 mt-1 flex justify-between items-center">
                <p v-if="errors.password" class="text-xs text-red-500">{{ errors.password }}</p>
                <button
                  type="button"
                  class="text-sm text-blue-600 hover:text-blue-700 font-medium"
                  @click="showForgotPasswordModal = true"
                >
                  忘记密码？
                </button>
              </div>
            </div>

            <!-- 记住我和用户协议 -->
            <div>
              <label class="flex items-start space-x-3 cursor-pointer">
                <div class="relative flex items-center justify-center mt-0.5">
                  <input
                    v-model="rememberMe"
                    type="checkbox"
                    v-bind="rememberMeAttrs"
                    class="appearance-none w-4 h-4 border-2 border-gray-300 rounded checked:bg-blue-600 checked:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                  />
                  <svg
                    class="absolute w-3 h-3 text-white pointer-events-none opacity-0 transition-opacity"
                    :class="{ 'opacity-100': rememberMe }"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="3"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <div class="text-sm text-gray-600">
                  我已阅读并同意
                  <span class="text-blue-600 hover:text-blue-700 font-medium">用户协议</span>
                </div>
              </label>
              <div class="h-5 mt-1">
                <p v-if="errors.rememberMe" class="text-xs text-red-500">{{ errors.rememberMe }}</p>
              </div>
            </div>

            <!-- 错误信息显示区域 -->
            <div v-if="errorMessage" class="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p class="text-sm text-red-600">{{ errorMessage }}</p>
            </div>

            <!-- 登录按钮 -->
            <button
              type="submit"
              class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="isLoading"
            >
              <div class="flex items-center justify-center">
                <span v-if="isLoading" class="flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    />
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  登录中...
                </span>
                <span v-else>登录</span>
              </div>
            </button>
          </form>

          <!-- 分隔线 -->
          <div class="my-6 flex items-center">
            <div class="flex-grow h-px bg-gray-200"></div>
            <span class="px-4 text-sm text-gray-400">或</span>
            <div class="flex-grow h-px bg-gray-200"></div>
          </div>

          <!-- 注册链接 -->
          <div class="text-center">
            <span class="text-sm text-gray-600">还没有账号？</span>
            <button
              type="button"
              class="text-sm font-medium text-blue-600 hover:text-blue-700 ml-1"
              @click="goToRegister"
            >
              立即注册
            </button>
          </div>

          <!-- 页脚 -->
          <div class="text-center mt-6 pt-6 border-t border-gray-200">
            <p class="text-xs text-gray-500">
              遇到问题？请联系
              <a
                href="mailto:<EMAIL>"
                class="text-blue-600 hover:text-blue-700"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码模态框 -->
    <ForgotPasswordModal
      v-if="showForgotPasswordModal"
      @close="showForgotPasswordModal = false"
      @handle-input-keydown="handleInputKeydown"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { login } from '@renderer/api/uniform/auth'
import { useUserStore } from '@renderer/stores/user'
import { EyeClosedIcon, EyeIcon } from 'lucide-vue-next'
import { emailRegex, phoneRegex } from '@renderer/utils/regex'
import { InitializationService } from '@renderer/services/InitializationService'
import * as yup from 'yup'
import ForgotPasswordModal from './ForgotPasswordModal.vue'

const router = useRouter()

// 表单验证规则
const { errors, handleSubmit, defineField } = useForm({
  validationSchema: yup.object({
    email: yup
      .string()
      .required('手机号或邮箱不能为空')
      .test('email-or-phone', '请输入正确的手机号或邮箱', (value) => {
        if (!value) return false
        return emailRegex.test(value) || phoneRegex.test(value)
      }),
    password: yup
      .string()
      .required('密码不能为空')
      .min(6, '密码至少需要 6 个字符')
      .max(20, '密码最多只能有 20 个字符'),
    rememberMe: yup.boolean().equals([true], '请同意用户协议').required('请确认是否同意用户协议')
  })
})

const [email, emailAttrs] = defineField('email')
const [password, passwordAttrs] = defineField('password')
const [rememberMe, rememberMeAttrs] = defineField('rememberMe')

const showPassword = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const showForgotPasswordModal = ref(false)

const goToRegister = () => {
  router.push('/register')
}

const submitForm = handleSubmit(async (_values) => {
  errorMessage.value = ''
  try {
    isLoading.value = true
    let flag: number = 0
    if (emailRegex.test(email.value)) {
      flag = 1
    } else if (phoneRegex.test(email.value)) {
      flag = 2
    } else {
      return
    }

    try {
      const res = await login(email.value, password.value, flag)
      if (res.data.code === 0) {
        const userStore = useUserStore()
        if (res.data.data?.token) {
          userStore.setToken(res.data.data.token)
          await userStore.setUserInfo({
            email: res.data.data.email,
            uuid: res.data.data.uuid
          })

          try {
            await InitializationService.initUserData()
          } catch (error) {
            console.error('Failed to initialize user data:', error)
            // 如果初始化失败，显示错误信息
            errorMessage.value = '数据初始化失败，请重试'
            return
          }

          await router.push('/')
        } else {
          errorMessage.value = '登录响应缺少令牌信息'
          console.error('Login response missing token:', res.data)
        }
      } else {
        errorMessage.value = res.data.message || '登录失败，请检查您的凭据'
      }
    } catch (error) {
      console.error('Login error:', error)
      errorMessage.value = '网络错误，请检查您的网络连接并重试'
    }
  } finally {
    isLoading.value = false
  }
})

// 输入框全选处理函数
const handleInputKeydown = (e: KeyboardEvent) => {
  if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
    e.preventDefault()
    const target = e.target as HTMLInputElement
    target.select()
  }
}
</script>

<style scoped>
/* Web 平台特定样式 */
</style>
