import fs from 'fs-extra'
import { writeFile } from 'fs/promises'
import { safelyRegisterHandler } from '../ipcHandlers'
import { downloadFile, openDialog } from './systemOperation'
import { showFileOpenDialog, showOpenDialog, showSaveDialog } from './dialog'
import type { FileFilter } from 'electron'

interface DownloadOptions {
  defaultPath?: string
  filters?: FileFilter[]
}

/**
 * Register all file system handlers
 */
export function registerFileSystemHandlers(): void {
  // Register all file system handlers
  registerBasicFileHandlers()
  registerDirectoryHandlers()
  registerJsonHandlers()
  registerDialogHandlers()
  registerBinaryFileHandlers()
}

/**
 * Register basic file operation handlers
 */
function registerBasicFileHandlers(): void {
  // Basic file operations
  safelyRegisterHandler('fs:writeFile', async (_, path: string, data: string) => {
    try {
      await fs.writeFile(path, data)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:readFileSync', async (_, file: string) => {
    return fs.readFileSync(file)
  })

  safelyRegisterHandler('fs:copyFile', async (_, src: string, dest: string) => {
    try {
      await fs.copyFile(src, dest)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  // Recursive directory copy
  safelyRegisterHandler('fs:copy', async (_, src: string, dest: string) => {
    try {
      console.log(`Copying from ${src} to ${dest}`)
      await fs.copy(src, dest, { overwrite: true })
      return { success: true }
    } catch (error: any) {
      console.error('Error in fs:copy:', error)
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:remove', async (_, path: string) => {
    try {
      await fs.remove(path)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:stat', async (_, path: string) => {
    try {
      const stats = fs.statSync(path)
      return { success: true, stats }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:isDirectory', async (_, path: string) => {
    try {
      const stats = await fs.stat(path)
      return { success: true, isDirectory: stats.isDirectory() }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:downloadFile', async (_, content: string, options?: DownloadOptions) => {
    return await downloadFile(content, options)
  })

  safelyRegisterHandler('fs:openDialog', async () => {
    return await openDialog()
  })
}

/**
 * Register directory operation handlers
 */
function registerDirectoryHandlers(): void {
  safelyRegisterHandler('fs:ensureDir', async (_, path: string) => {
    try {
      await fs.ensureDir(path)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:ensureFile', async (_, path: string) => {
    try {
      await fs.ensureFile(path)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:mkdirs', async (_, dir: string) => {
    try {
      await fs.mkdirs(dir)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  safelyRegisterHandler('fs:readdir', async (_, path: string) => {
    try {
      const files = await fs.readdir(path)
      return { success: true, files }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
}

/**
 * Register JSON file operation handlers
 */
function registerJsonHandlers(): void {
  safelyRegisterHandler(
    'fs:writeJson',
    async (_, file: string, object: any, options?: fs.WriteOptions) => {
      try {
        await fs.writeFile(file, object, options)
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  )

  safelyRegisterHandler(
    'fs:outputJson',
    async (_, file: string, object: any, options?: fs.WriteOptions) => {
      try {
        await fs.outputJson(file, object, options)
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  )

  safelyRegisterHandler('fs:readJson', async (_, file: string, options?: fs.ReadOptions) => {
    try {
      const data = await fs.readJson(file, options)
      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
}

/**
 * Register dialog operation handlers
 */
function registerDialogHandlers(): void {
  safelyRegisterHandler('fs:showSaveDialog', async (_, name: string) => {
    return await showSaveDialog(name)
  })

  safelyRegisterHandler('fs:showOpenDialog', async () => {
    return await showOpenDialog()
  })

  safelyRegisterHandler(
    'fs:showFileOpenDialog',
    async (
      _,
      options?: {
        title?: string
        buttonLabel?: string
        filters?: { name: string; extensions: string[] }[]
      }
    ) => {
      return await showFileOpenDialog(options)
    }
  )
}

/**
 * Register binary file operation handlers
 */
function registerBinaryFileHandlers(): void {
  safelyRegisterHandler('fs:writeBinaryFile', async (_event, filePath: string, data: ArrayBuffer) => {
    try {
      await writeFile(filePath, Buffer.from(data))
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })

  /**
   * Read binary file
   */
  safelyRegisterHandler('fs:readBinaryFile', async (_event, filePath: string) => {
    try {
      // Use Node.js fs module to read binary file
      const data = await fs.readFile(filePath)
      // Return ArrayBuffer for use in renderer process
      return {
        success: true,
        data: data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength)
      }
    } catch (error: any) {
      console.error('Failed to read binary file:', error)
      return { success: false, error: error.message }
    }
  })
}
