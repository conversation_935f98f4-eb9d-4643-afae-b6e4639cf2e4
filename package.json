{"name": "chronnote", "version": "0.2.0", "description": "一款面向未来的笔记应用", "main": "./out/main/index.js", "author": "Chron Team", "homepage": "http://chronnote.top", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "checks": "node scripts/checks/index.js", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "test": "electron-vite dev --mode production", "vitest": "vitest", "test:unit": "vitest run", "build": "pnpm run checks && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "pnpm run checks && pnpm run build && electron-builder --dir", "build:win": "pnpm run checks && pnpm run build && electron-builder --win --x64", "build:mac": "pnpm run checks && pnpm run build && electron-builder --mac --arm64 --universal", "build:linux": "pnpm run checks && pnpm run build && electron-builder --linux", "dev:web": "cross-env BUILD_TARGET=web vite dev --config web.vite.config.ts", "build:web": "pnpm run checks && cross-env BUILD_TARGET=web electron-vite build", "release": "python scripts/release/main.py", "icon": "node scripts/icon-config-generator.js", "icon:scan": "node scripts/icon-config-generator.js", "icon:force": "rm -f .icon-state.json && node scripts/icon-config-generator.js"}, "dependencies": {"better-sqlite3": "^11.10.0", "dexie": "^4.0.11", "electron-log": "^5.4.0", "electron-updater": "^6.6.2", "fs-extra": "^11.3.0", "pinyin-pro": "^3.26.0"}, "devDependencies": {"@capacitor-community/sqlite": "^7.0.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/filesystem": "^7.0.1", "@capacitor/ios": "^7.2.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.0", "@electron-toolkit/eslint-config": "^2.1.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/tsconfig": "^1.0.1", "@electron-toolkit/utils": "^4.0.0", "@electron/rebuild": "^3.7.2", "@floating-ui/core": "^1.7.0", "@floating-ui/dom": "^1.7.0", "@hocuspocus/transformer": "^3.1.1", "@leafer-in/animate": "^1.7.0", "@leafer-in/find": "^1.7.0", "@leafer-in/motion-path": "^1.7.0", "@leafer-in/resize": "^1.7.0", "@radix-icons/vue": "^1.0.0", "@rushstack/eslint-patch": "^1.11.0", "@sentry/electron": "^6.5.0", "@sentry/vue": "^9.22.0", "@tailwindcss/vite": "^4.1.7", "@tanstack/vue-table": "^8.21.3", "@tiptap-pro/extension-drag-handle": "3.0.0-beta.18", "@tiptap-pro/extension-drag-handle-vue-3": "3.0.0-beta.18", "@tiptap-pro/extension-file-handler": "3.0.0-beta.18", "@tiptap-pro/extension-mathematics": "3.0.0-beta.18", "@tiptap-pro/extension-node-range": "3.0.0-beta.18", "@tiptap-pro/extension-unique-id": "3.0.0-beta.18", "@tiptap/core": "3.0.0-beta.5", "@tiptap/extension-blockquote": "3.0.0-beta.5", "@tiptap/extension-bold": "3.0.0-beta.5", "@tiptap/extension-bubble-menu": "3.0.0-beta.5", "@tiptap/extension-bullet-list": "3.0.0-beta.5", "@tiptap/extension-code-block-lowlight": "3.0.0-beta.5", "@tiptap/extension-collaboration": "3.0.0-beta.5", "@tiptap/extension-document": "3.0.0-beta.5", "@tiptap/extension-horizontal-rule": "3.0.0-beta.5", "@tiptap/extension-link": "3.0.0-beta.5", "@tiptap/extension-list": "3.0.0-beta.5", "@tiptap/extension-mention": "3.0.0-beta.5", "@tiptap/extension-text": "^2.12.0", "@tiptap/extension-underline": "3.0.0-beta.5", "@tiptap/extensions": "3.0.0-beta.5", "@tiptap/html": "3.0.0-beta.5", "@tiptap/pm": "3.0.0-beta.5", "@tiptap/starter-kit": "3.0.0-beta.5", "@tiptap/suggestion": "3.0.0-beta.5", "@tiptap/vue-3": "3.0.0-beta.5", "@tiptap/y-tiptap": "3.0.0-beta.3", "@types/better-sqlite3": "^7.6.13", "@types/dexie": "^1.3.35", "@types/dompurify": "^3.2.0", "@types/node": "^22.14.1", "@vaadin/split-layout": "^24.7.3", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "chalk": "^4.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "cypress": "^14.3.2", "daisyui": "^5.0.27", "dockview-vue": "^4.4.0", "dotenv": "^16.5.0", "electron": "^35.2.1", "electron-builder": "^26.0.12", "electron-icon-maker": "^0.0.5", "electron-vite": "^3.1.0", "embla-carousel-vue": "^8.6.0", "esbuild-register": "^3.6.0", "eslint": "^9.25.1", "eslint-plugin-vue": "^10.0.0", "highlight.js": "^11.11.1", "husky": "^9.1.7", "interactjs": "^1.10.27", "js-pinyin": "^0.2.7", "jwt-decode": "^4.0.0", "katex": "^0.16.22", "langchain": "^0.3.24", "leafer-editor": "^1.7.0", "leafer-ui": "^1.7.0", "leafer-x-connector": "^0.1.3", "lit": "^3.3.0", "lowlight": "^3.3.0", "lucide-vue-next": "^0.503.0", "markdown-it": "^14.1.0", "markdown-it-mathjax3": "^4.3.2", "marked": "^15.0.10", "marked-highlight": "^2.2.1", "mitt": "^3.0.1", "moment": "^2.30.1", "motion-v": "^1.0.1", "naive-ui": "^2.41.0", "path-browserify-esm": "^1.0.6", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "radix-vue": "^1.9.17", "reka-ui": "^2.2.0", "sass-embedded": "^1.87.0", "scss": "^0.2.4", "semver": "^7.7.1", "splitpanes": "^4.0.3", "tailwind-merge": "^3.2.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.4", "terser": "^5.39.0", "tiptap-extension-auto-joiner": "^0.1.3", "tiptap-extension-global-drag-handle": "^0.1.18", "turndown": "^7.2.0", "tw-animate-css": "^1.2.8", "typescript": "^5.8.3", "uuid": "^11.1.0", "vee-validate": "^4.15.0", "vite": "^6.3.3", "vite-plugin-vue-devtools": "^7.7.5", "vitest": "^3.1.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-tsc": "^2.2.10", "web-animations-js": "^2.3.2", "y-prosemirror": "^1.3.4", "y-protocols": "^1.0.6", "yjs": "^13.6.27", "yup": "^1.6.1"}, "build": {"mac": {"icon": "build/chronnote_mac.png"}, "asar": true, "files": ["out/**/*"]}, "packageManager": "pnpm@10.12.1", "pnpm": {"neverBuiltDependencies": []}}